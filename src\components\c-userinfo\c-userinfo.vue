<script setup>
import { ref } from 'vue'

// Reactive state
const value = ref('请填写')
const sex = ref([
  { id: 1, name: '男' },
  { id: 2, name: '女' },
])
const index = ref(0)
const region = ref(['请填写'])
const date = ref('请填写')
const avater = ref('')
const description = ref('')
const url = ref('')
const nickName = ref('')
const mobile = ref('')
const headimg = ref('')

// Methods
function bindPickerChange(e) {
  index.value = e.detail.value
}

function bindRegionChange(e) {
  region.value = e.detail.value
}

function bindDateChange(e) {
  date.value = e.detail.value
}

function bindnickName(e) {
  nickName.value = e.detail.value
}

function bindmobile(e) {
  mobile.value = e.detail.value
}

function binddescription(e) {
  description.value = e.detail.value
}

function avatarChoose() {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success(res) {
      imgUpload(res.tempFilePaths)
    },
  })
}

function getUserInfo() {
  uni.getUserProfile({
    desc: '用于完善会员资料',
    success: (res) => {
      console.log(res)
      uni.showToast({
        title: '已授权',
        icon: 'none',
        duration: 2000,
      })
    },
  })
}

function getphonenumber(e) {
  if (e.detail.iv) {
    console.log(e.detail.iv)
    uni.showToast({
      title: '已授权',
      icon: 'none',
      duration: 2000,
    })
  }
}

function savaInfo() {
  const nickname = nickName.value
  const headimgValue = headimg.value || avater.value
  const gender = index.value + 1
  const mobileValue = mobile.value
  const regionValue = region.value
  const birthday = date.value
  const descriptionValue = description.value

  if (!nickname) {
    uni.showToast({
      title: '请填写昵称',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  const updata = { nickname, headimg: headimgValue, gender }

  if (!isPoneAvailable(mobileValue)) {
    uni.showToast({
      title: '手机号码有误，请重填',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  updata.mobile = mobileValue

  if (regionValue.length === 1) {
    uni.showToast({
      title: '请选择常住地',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  else {
    updata.province = regionValue[0]
    updata.city = regionValue[1]
    updata.area = regionValue[2]
  }

  if (birthday === '0000-00-00') {
    uni.showToast({
      title: '请选择生日',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  updata.birthday = birthday
  updata.description = descriptionValue
  updataToBackend(updata)
}

function isPoneAvailable(poneInput) {
  const myreg = /^1[3,4578]\d{9}$/
  return myreg.test(poneInput)
}

function updataToBackend(datas) {
  // 传后台
}

function imgUpload(file) {
  uni.uploadFile({
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    url: '/api/upload/image', // 图片上传接口
    filePath: file[0],
    name: 'file',
    formData: {
      type: 'user_headimg',
    },
    success(res) {
      const data = JSON.parse(res.data).data
      avater.value = url.value + data.img
      headimg.value = url.value + data.img
    },
    fail(error) {
      console.log(error)
    },
  })
}
</script>

<template>
  <view class="container">
    <view class="ui-all">
      <view class="avatar" @tap="avatarChoose">
        <view class="imgAvatar">
          <view class="iavatar" :style="`background: url(${avater}) no-repeat center/cover #eeeeee;`" />
        </view>
        <text v-if="avater">
          修改头像
        </text>
        <text v-if="!avater">
          授权微信
        </text>
        <button v-if="!avater" open-type="getUserInfo" class="getInfo" @tap="getUserInfo" />
      </view>
      <view class="ui-list">
        <text>昵称</text>
        <input
          type="text" :placeholder="value" :value="nickName" placeholder-class="place"
          @input="bindnickName"
        >
      </view>
      <view class="ui-list">
        <text>手机号</text>
        <input
          v-if="mobile" type="tel" :placeholder="value" :value="mobile" placeholder-class="place"
          @input="bindmobile"
        >
        <button v-if="!mobile" open-type="getPhoneNumber" class="getInfo bun" @getphonenumber="getphonenumber">
          授权手机号
        </button>
      </view>
      <view class="ui-list right">
        <text>性别</text>
        <picker mode="selector" range-key="name" :value="index" :range="sex" @change="bindPickerChange">
          <view class="picker">
            {{ sex[index].name }}
          </view>
        </picker>
      </view>
      <view class="ui-list right">
        <text>常住地</text>
        <picker mode="region" @change="bindRegionChange">
          <view class="picker">
            {{ region[0] }} {{ region[1] }} {{ region[2] }}
          </view>
        </picker>
      </view>
      <view class="ui-list right">
        <text>生日</text>
        <picker mode="date" :value="date" @change="bindDateChange">
          <view class="picker">
            {{ date }}
          </view>
        </picker>
      </view>
      <view class="ui-list">
        <text>签名</text>
        <textarea
          :placeholder="value" placeholder-class="place" :value="description"
          @input="binddescription"
        />
      </view>
      <button class="save" @tap="savaInfo">
        保 存 修 改
      </button>
    </view>
  </view>
</template>

<style lang="less">
.container {
  display: block;
}

.ui-all {
  padding: 20rpx 40rpx;

  .avatar {
    width: 100%;
    text-align: left;
    padding: 20rpx 0;
    border-bottom: solid 1px #f2f2f2;
    position: relative;

    .imgAvatar {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      overflow: hidden;

      .iavatar {
        width: 100%;
        height: 100%;
        display: block;
      }
    }

    text {
      display: inline-block;
      vertical-align: middle;
      color: #8e8e93;
      font-size: 28rpx;
      margin-left: 40rpx;
    }

    &:after {
      content: ' ';
      width: 20rpx;
      height: 20rpx;
      border-top: solid 1px #030303;
      border-right: solid 1px #030303;
      transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      /* IE 9 */
      -moz-transform: rotate(45deg);
      /* Firefox */
      -webkit-transform: rotate(45deg);
      /* Safari 和 Chrome */
      -o-transform: rotate(45deg);
      position: absolute;
      top: 85rpx;
      right: 0;
    }
  }

  .ui-list {
    width: 100%;
    text-align: left;
    padding: 20rpx 0;
    border-bottom: solid 1px #f2f2f2;
    position: relative;

    text {
      color: #4a4a4a;
      font-size: 28rpx;
      display: inline-block;
      vertical-align: middle;
      min-width: 150rpx;
    }

    input {
      color: #030303;
      font-size: 30rpx;
      display: inline-block;
      vertical-align: middle;
    }

    button {
      color: #030303;
      font-size: 30rpx;
      display: inline-block;
      vertical-align: middle;
      background: none;
      margin: 0;
      padding: 0;

      &::after {
        display: none;
      }
    }

    picker {
      width: 90%;
      color: #030303;
      font-size: 30rpx;
      display: inline-block;
      vertical-align: middle;
      position: absolute;
      top: 30rpx;
      left: 150rpx;
    }

    textarea {
      color: #030303;
      font-size: 30rpx;
      vertical-align: middle;
      height: 150rpx;
      width: 100%;
      margin-top: 50rpx;
    }

    .place {
      color: #999999;
      font-size: 28rpx;
    }
  }

  .right:after {
    content: ' ';
    width: 20rpx;
    height: 20rpx;
    border-top: solid 1px #030303;
    border-right: solid 1px #030303;
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    /* IE 9 */
    -moz-transform: rotate(45deg);
    /* Firefox */
    -webkit-transform: rotate(45deg);
    /* Safari 和 Chrome */
    -o-transform: rotate(45deg);
    position: absolute;
    top: 40rpx;
    right: 0;
  }

  .save {
    background: #030303;
    border: none;
    color: #ffffff;
    margin-top: 40rpx;
    font-size: 28rpx;
  }
}
</style>
