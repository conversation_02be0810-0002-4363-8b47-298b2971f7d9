import { http } from '@/utils/http'

/**
 * 微信手机号解密请求参数
 */
export interface IDecryptPhoneRequest {
  encryptedData: string
  iv: string
  code: string
  dataType: 'phoneNumber'
}

/**
 * 微信手机号解密响应
 */
export interface IDecryptPhoneResponse {
  phoneNumber: string
  purePhoneNumber: string
  countryCode: string
}

/**
 * 解密微信手机号
 * @param data 解密请求参数
 */
export function decryptPhoneNumber(data: IDecryptPhoneRequest) {
  return http.post<IDecryptPhoneResponse>('/users/decrypt-user-info', data)
}
