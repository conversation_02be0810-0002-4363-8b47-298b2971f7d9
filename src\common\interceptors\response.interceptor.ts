/**
 * 全局响应拦截器
 * 用于处理后端统一返回格式
 */

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

/**
 * 处理后端API响应的工具函数
 * @param response 原始响应数据
 * @returns 处理后的数据
 */
export function handleApiResponse(response: any): any {
  // 检查是否是后端统一格式
  if (response && typeof response === 'object' && 'code' in response && 'message' in response && 'data' in response) {
    // 检查业务状态码
    if (response.code === 200) {
      return response.data
    }
    else {
      // 业务错误，抛出异常
      throw new Error(response.message || `请求失败，错误代码：${response.code}`)
    }
  }

  // 如果不是统一格式，直接返回
  return response
}

/**
 * 处理聊天API响应
 * @param response 聊天API响应
 * @returns 处理后的聊天数据
 */
export function handleChatApiResponse(response: any): any {
  try {
    // 处理统一响应格式
    const data = handleApiResponse(response)
    return data
  }
  catch (error) {
    console.error('处理聊天API响应失败:', error)
    throw error
  }
}
