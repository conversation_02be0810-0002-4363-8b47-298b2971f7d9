import { CozeAPI } from '@coze/uniapp-api'

export function createCozeClient() {
  return new CozeAPI({
    baseURL: import.meta.env.VITE_COZE_BASE_URL || 'https://api.coze.cn',
    token: import.meta.env.VITE_COZE_TOKEN || 'pat_GDsN2EVvBsK3Uf0AeD94Uc97oMzIEZPWvNaR3VDhBXY3hP6RWcHW7SvXeCi1fpr8',
    allowPersonalAccessTokenInBrowser: true, // only for test
  })
}

export const cozeClient = createCozeClient()
