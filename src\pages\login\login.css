.main-content {
  /* background: linear-gradient(180deg, #667eea 0%, #764ba2 100%); */
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.main-content::-webkit-scrollbar {
  display: none;
}

.login-container {
  padding: 40px 20px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.logo-icon {
  font-size: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-name {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.app-tagline {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.login-form {
  background: white;
  border-radius: 24px;
  padding: 32px 24px;
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  margin-bottom: 20px;
}

.form-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  display: block;
}

.form-input {
  height: 48px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #1e293b;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: #94a3b8;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  cursor: pointer;
  font-size: 20px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.checkbox:checked {
  background: #667eea;
  border-color: #667eea;
}

.forgot-password {
  font-size: 14px;
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.login-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.divider {
  text-align: center;
  position: relative;
  margin: 20px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.divider-text {
  background: white;
  padding: 0 16px;
  color: #94a3b8;
  font-size: 14px;
}

.social-login {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.social-button {
  flex: 1;
  height: 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-button:hover {
  border-color: #d1d5db;
  background: #f8fafc;
}

.wechat {
  color: #07c160;
}

.apple {
  color: #000;
}

.qq {
  color: #1296db;
}

.signup-link {
  text-align: center;
  padding: 20px;
}

.signup-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.signup-button {
  color: white;
  font-weight: 600;
  text-decoration: none;
  margin-left: 4px;
}

.quick-login {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-login-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.quick-login-buttons {
  display: flex;
  gap: 12px;
}

.quick-button {
  flex: 1;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s infinite linear;
}

.particle:nth-child(1) {
  width: 4px;
  height: 4px;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.particle:nth-child(2) {
  width: 6px;
  height: 6px;
  left: 20%;
  animation-delay: 2s;
  animation-duration: 10s;
}

.particle:nth-child(3) {
  width: 3px;
  height: 3px;
  left: 70%;
  animation-delay: 4s;
  animation-duration: 7s;
}

.particle:nth-child(4) {
  width: 5px;
  height: 5px;
  left: 80%;
  animation-delay: 1s;
  animation-duration: 9s;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}
