<div align="center">

# 🚀 Kanli

**基于 uni-app 的现代化跨平台前端框架，使用unibest为uniapp框架**

[![Node Version](https://img.shields.io/badge/node-%3E%3D18-green.svg)](https://nodejs.org/)
[![PNPM Version](https://img.shields.io/badge/pnpm-%3E%3D7.30-green.svg)](https://pnpm.io/)
[![License](https://img.shields.io/github/license/feige996/unibest)](https://opensource.org/license/mit/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Vue 3](https://img.shields.io/badge/Vue-3.0+-4FC08D.svg)](https://vuejs.org/)

[📖 文档地址](https://unibest.tech/) • [📱 在线演示](https://feige996.github.io/hello-unibest/) • [🚀 快速开始](#-快速开始)

</div>

---

## ✨ 特性

- 🔥 **现代化技术栈** - Vue 3 + TypeScript + Vite，享受最新的开发体验
- 📱 **多端兼容** - 一套代码运行于 H5、小程序、App 等多个平台
- 🎯 **开箱即用** - 内置常用功能和最佳实践，快速启动项目
- 🔐 **完整的权限系统** - 内置登录、权限验证、路由守卫等功能
- 📦 **模块化架构** - 清晰的项目结构，便于维护和扩展
- 🌈 **响应式设计** - 适配各种屏幕尺寸和设备
- 🔧 **丰富的工具链** - 集成 ESLint、Prettier、UnoCSS 等工具

## 🌍 平台兼容性

| 平台 | H5 | iOS | Android | 微信小程序 | 字节小程序 | 快手小程序 | 支付宝小程序 | 钉钉小程序 | 百度小程序 |
|------|----|----|---------|------------|------------|------------|--------------|------------|------------|
| 状态 | ✅ | ✅ | ✅      | ✅         | ✅         | ✅         | ✅           | ✅         | ✅         |

> **注意**: 每种 UI 框架支持的平台有所不同，详情请查看各 UI 框架的官网或 unibest 文档。

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **跨平台框架**: uni-app
- **状态管理**: Pinia
- **HTTP 请求**: Alova + uni.request
- **构建工具**: Vite
- **CSS 框架**: SCSS + UnoCSS
- **数据请求**: @tanstack/vue-query
- **日期处理**: dayjs

## ⚙️ 环境要求

- **Node.js**: >= 18
- **pnpm**: >= 7.30
- **Vue Official**: >= 2.1.10
- **TypeScript**: >= 5.0

## 🚀 快速开始

### 1. 创建项目

```bash
# 使用模板创建项目
pnpm create unibest my-project

# 或者克隆仓库
git clone https://github.com/your-repo/kanli.git
cd kanli
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 启动开发服务器

```bash
# H5 开发
pnpm dev

# 微信小程序开发
pnpm dev:mp

# App 开发
pnpm dev:app
```

## 📦 开发与构建

### 开发模式（支持热更新）

```bash
# H5 开发 - 访问 http://localhost:9000/
pnpm dev:h5

# 微信小程序开发 - 在微信开发者工具中打开 dist/dev/mp-weixin
pnpm dev:mp

# App 开发 - 在 HBuilderX 中打开 dist/dev/app
pnpm dev:app
```

### 生产构建

```bash
# H5 构建
pnpm build:h5

# 微信小程序构建
pnpm build:mp

# App 构建
pnpm build:app
```

## 📁 项目结构

```
kanli/
├── src/
│   ├── api/                 # API 接口定义
│   ├── components/          # Vue 组件
│   ├── hooks/               # Vue 自定义钩子
│   ├── interceptors/        # 请求和路由拦截器
│   ├── layouts/             # 页面布局组件
│   ├── pages/               # 主要页面
│   ├── pages-sub/           # 子页面
│   ├── service/             # 业务逻辑服务
│   ├── static/              # 静态资源文件
│   ├── store/               # Pinia 状态管理
│   ├── utils/               # 工具函数
│   └── main.ts              # 应用入口文件
├── env/                     # 环境配置文件
├── patches/                 # 第三方库补丁
├── scripts/                 # 脚本文件
├── vite-plugins/            # Vite 插件配置
├── manifest.config.ts       # 应用配置
├── pages.config.ts          # 页面配置
├── vite.config.ts           # Vite 配置
└── uno.config.ts            # UnoCSS 配置
```

## 🔧 核心功能

### 权限管理

项目内置完整的权限管理系统，包括：

- 🔐 用户登录/注册
- 🛡️ 路由守卫
- 👤 用户信息管理
- 🔑 Token 自动刷新

### 请求管理

- 📡 统一的 API 接口管理
- 🔄 请求/响应拦截器
- ⚡ 基于 Alova 的高性能请求
- 🎯 TypeScript 类型安全

### 状态管理

- 📊 基于 Pinia 的状态管理
- 💾 持久化存储
- 🔄 响应式数据更新

## 🎯 快速上手

### 创建新页面

```bash
# 在 src/pages 目录下创建新页面
mkdir src/pages/my-page
touch src/pages/my-page/index.vue
```

### 添加 API 接口

```typescript
// src/api/my-api.ts
import { request } from '@/utils/request'

export const getMyData = () => {
  return request.get('/my-data')
}
```

### 使用状态管理

```typescript
// src/store/my-store.ts
import { defineStore } from 'pinia'

export const useMyStore = defineStore('my-store', {
  state: () => ({
    data: null
  }),
  actions: {
    async fetchData() {
      // 获取数据逻辑
    }
  }
})
```

## 🚀 部署

### H5 部署

```bash
pnpm build:h5
# 构建文件位于 dist/build/h5
```

### 微信小程序部署

```bash
pnpm build:mp
# 在微信开发者工具中打开 dist/build/mp-weixin
# 点击右上角"上传"按钮上传版本
```

### App 部署

```bash
pnpm build:app
# 在 HBuilderX 中打开 dist/build/app
# 选择发行 -> APP 云打包
```

## 📝 更新日志

查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细的更新记录。

## 🤝 贡献指南

我们欢迎所有的贡献！请阅读 [贡献指南](./CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目基于 [MIT](https://opensource.org/license/mit/) 许可证开源。

## 🙏 鸣谢

感谢以下优秀的开源项目：

- [uni-app](https://uniapp.dcloud.net.cn/) - 跨平台应用开发框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Vite](https://vitejs.dev/) - 现代化构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理库

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

[报告问题](https://github.com/your-repo/kanli/issues) • [请求功能](https://github.com/your-repo/kanli/issues) • [讨论交流](https://github.com/your-repo/kanli/discussions)

</div>