.profile {
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f8f8fc;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.view-all-btn {
  font-size: 28rpx;
  color: #6a5acd;
  background: none;
  padding: 0;
  border: none;
}

/* Header */
.profile-header {
  margin-bottom: 40rpx;
}

.header-container {
  background: linear-gradient(135deg, #6a5acd, #9370db);
  border-radius: 24rpx;
  padding: 30rpx;
  color: white;
  box-shadow: 0 8rpx 16rpx rgba(106, 90, 205, 0.2);
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.settings-btn {
  height: 64rpx;
  width: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0;
  border: none;
  color: white;
  font-size: 36rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: 24rpx;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 4rpx solid white;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.user-badges {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.badge {
  padding: 6rpx 12rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  color: white;
}

.badge-membership {
  background-color: rgba(255, 152, 0, 0.7);
}

.badge-type {
  background-color: rgba(255, 255, 255, 0.2);
}

.badge-separator {
  margin: 0 12rpx;
  color: rgba(255, 255, 255, 0.6);
  font-size: 22rpx;
}

.user-id,
.user-phone {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-actions {
  display: flex;
  margin-top: 20rpx;
  gap: 20rpx;
}

.action-btn {
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: medium;
  border: none;
}

.edit-btn {
  background-color: white;
  color: #6a5acd;
}

.logout-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Stats Card */
.stats-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-item {
  width: 48%;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  background-color: #f0f8ff;
  border-radius: 16rpx;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-assessment {
  color: #4285f4;
}

.stat-free {
  color: #34a853;
}

.stat-ai {
  color: #6a5acd;
}

.stat-free-ai {
  color: #ff9800;
}

.stat-label {
  font-size: 22rpx;
  color: #555555;
}

/* Membership Card */
.membership-card {
  background: linear-gradient(135deg, #f0f8ff, #f8f8fc);
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.membership-title {
  font-size: 26rpx;
  color: #555555;
  margin-bottom: 16rpx;
}

.membership-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.membership-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 6rpx;
}

.membership-expire {
  font-size: 22rpx;
  color: #999999;
}

.upgrade-btn {
  background: linear-gradient(135deg, #ff9800, #ff5722);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
}

.upgrade-text {
  font-size: 24rpx;
  color: white;
}

/* Account Info */
.account-info {
  padding-top: 24rpx;
}

.account-item {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.account-item:last-child {
  border-bottom: none;
}

.account-label {
  font-size: 26rpx;
  color: #555555;
}

.account-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.team-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.status-active {
  background-color: #34a853;
}

.status-inactive {
  background-color: #999999;
}

/* Invite Card */
.invite-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.invite-code-container {
  margin-bottom: 24rpx;
}

.invite-code {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.invite-code-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #6a5acd;
  font-family: monospace;
  letter-spacing: 2rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  border: none;
}

.copy-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.copy-text {
  font-size: 24rpx;
  color: #555555;
}

.invite-stats {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.invite-stat-item {
  text-align: center;
}

.invite-stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  display: block;
}

.invite-stat-label {
  font-size: 22rpx;
  color: #999999;
}

.invite-loading {
  padding: 40rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

.invite-empty {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.invite-empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 20rpx;
}

.get-invite-btn {
  background-color: #6a5acd;
  color: white;
  font-size: 26rpx;
  padding: 16rpx 32rpx;
  border-radius: 30rpx;
  border: none;
}

.go-invite {
  margin-top: 24rpx;
  text-align: center;
}

.go-invite-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.go-invite-text {
  font-size: 28rpx;
  color: #6a5acd;
}

.go-invite-icon {
  font-size: 28rpx;
  color: #6a5acd;
  margin-left: 8rpx;
}

/* Achievements */
.achievements-scroll {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 10rpx;
}

.achievements-container {
  display: flex;
  padding: 10rpx 0;
  gap: 20rpx;
}

.achievement {
  position: relative;
  min-width: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  font-size: 40rpx;
}

.achievement-new {
  background-color: #e3f2fd;
  color: #2196f3;
}

.achievement-basic {
  background-color: #e8f5e9;
  color: #4caf50;
}

.achievement-active {
  background-color: #fff8e1;
  color: #ffc107;
}

.achievement-ai {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.achievement-loyal {
  background-color: #e0f7fa;
  color: #00bcd4;
}

.achievement-member {
  background-color: #fff3e0;
  color: #ff9800;
}

.achievement-name {
  font-size: 22rpx;
  color: #555555;
  text-align: center;
}

.achievement-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff5722;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

/* Activities */
.activities-section {
  margin-bottom: 150rpx;
  /* 为底部留出空间 */
}

.activities-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  display: flex;
}

.activity-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 40rpx;
}

.activity-assessment {
  background-color: #e3f2fd;
  color: #2196f3;
}

.activity-ai {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.activity-register {
  background-color: #e8f5e9;
  color: #4caf50;
}

.activity-login {
  background-color: #fff8e1;
  color: #ffc107;
}

.activity-details {
  padding-top: 6rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #999999;
}

.activity-time {
  font-size: 24rpx;
  color: #999999;
  align-self: flex-start;
  margin-top: 6rpx;
}

.no-activities {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-activities-icon {
  font-size: 80rpx;
  color: #cccccc;
  margin-bottom: 20rpx;
}

.no-activities-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

/* Login Guide */
.login-guide {
  margin: 60rpx 0;
  padding: 60rpx 40rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.login-guide-icon {
  font-size: 120rpx;
  color: #6a5acd;
  margin-bottom: 30rpx;
}

.login-guide-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  text-align: center;
}

.login-guide-text {
  font-size: 28rpx;
  color: #555555;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.login-guide-btn {
  background: linear-gradient(135deg, #6a5acd, #9370db);
  color: white;
  font-size: 30rpx;
  font-weight: medium;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 8rpx 16rpx rgba(106, 90, 205, 0.3);
}
