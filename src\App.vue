<script setup lang="ts">
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { usePageAuth } from '@/hooks/usePageAuth'
import { useUserStore } from '@/store/user'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'

usePageAuth()

// 初始化用户store
const userStore = useUserStore()

onLaunch(() => {
  console.log('App Launch')
  // 从本地存储初始化用户信息
  userStore.initUserDetails()
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
</style>
