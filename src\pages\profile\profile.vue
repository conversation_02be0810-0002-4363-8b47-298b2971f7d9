<route lang="json5" type="page">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '个人中心',
  },
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'Profile',
})

// 用户store
const userStore = useUserStore()

// 邀请功能相关
const inviteCode = ref('')
const inviteLoading = ref(false)
const inviteStats = ref({
  totalCount: 0,
  activeCount: 0,
  teamCount: 0,
})

// 格式化日期函数
function formatDate(dateString: string) {
  if (!dateString)
    return '未设置'
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取会员类型显示文本
function getMembershipText(type: string) {
  const typeMap: Record<string, string> = {
    free: '免费会员',
    basic: '基础会员',
    premium: '高级会员',
    vip: 'VIP会员',
  }
  return typeMap[type] || type
}

// 获取用户类型显示文本
function getUserTypeText(type: string) {
  const typeMap: Record<string, string> = {
    individual: '个人用户',
    team: '团队用户',
    enterprise: '企业用户',
  }
  return typeMap[type] || type
}

// 跳转到设置页面
function goToSettings() {
  uni.navigateTo({
    url: '/pages/settings/settings',
  })
}

// 编辑个人资料
function editProfile() {
  uni.navigateTo({
    url: '/pages/userinfo/userinfo',
  })
}

// 跳转到登录页面
function goToLogin() {
  uni.navigateTo({
    url: '/pages/login/login',
  })
}

// 跳转到邀请页面
function goToInvite() {
  uni.navigateTo({
    url: '/pages/invite/invite',
  })
}

function goToAbout() {
  uni.navigateTo({
    url: '/pages/about/about',
  })
}

// 跳转到购买会员页面
function goToMembership() {
  uni.navigateTo({
    url: '/pages/membership/membership',
  })
}

// 获取我的邀请码
async function getMyInviteCode() {
  if (!userStore.isLoggedIn || !userStore.userDetails?.hasTeamPermission)
    return

  inviteLoading.value = true

  try {
    // 这里应该是真实API调用，这里模拟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟API响应
    inviteCode.value = `TEAM${Math.random().toString(36).substring(2, 10).toUpperCase()}`

    // 模拟邀请数据
    inviteStats.value = {
      totalCount: Math.floor(Math.random() * 10),
      activeCount: Math.floor(Math.random() * 5),
      teamCount: Math.floor(Math.random() * 3),
    }
  }
  catch (err) {
    console.error('获取邀请码失败:', err)
  }
  finally {
    inviteLoading.value = false
  }
}

// 复制邀请码
function copyInviteCode() {
  if (!inviteCode.value)
    return

  uni.setClipboardData({
    data: inviteCode.value,
    success: () => {
      uni.showToast({
        title: '邀请码已复制',
        icon: 'success',
      })
    },
  })
}

// 页面加载时，如果已经登录且有团队权限，则获取邀请码
onMounted(() => {
  if (userStore.isLoggedIn && userStore.userDetails?.hasTeamPermission) {
    getMyInviteCode()
  }
})
</script>

<template>
  <view class="profile">
    <!-- 已登录状态 -->
    <view v-if="userStore.userDetails" class="profile-header">
      <view class="header-container">
        <view class="header-top">
          <h1 class="header-title">
            个人主页
          </h1>
          <button class="settings-btn" @click="goToSettings">
            <view class="i-material-symbols:settings" />
          </button>
        </view>
        <view class="user-info">
          <view class="avatar-container">
            <!-- 如果是微信小程序，对其进行更改设置 -->
            <!-- #ifdef MP-WEIXIN -->
            <image
              :src="`${userStore.userDetails.profilePicture}` || '/static/images/default-avatar.png'" alt="用户头像"
              class="avatar" mode="aspectFill"
            />
            <!-- #endif -->
            <!-- 除了微信小程序之外的其他平台 -->
            <!-- #ifndef MP-WEIXIN -->
            <image
              :src="userStore.userDetails.profilePicture || '/static/images/default-avatar.png'" alt="用户头像"
              class="avatar" mode="aspectFill"
            />
            <!-- #endif -->
          </view>
          <view class="user-details">
            <h2 class="user-name">
              {{ userStore.userDetails.name || '未设置' }}
            </h2>
            <view class="user-badges">
              <span class="badge badge-membership">
                {{ getMembershipText(userStore.userDetails.membershipType) }}
              </span>
              <span class="badge-separator">|</span>
              <span class="user-id">ID: {{ userStore.userDetails.id }}</span>
            </view>
            <view class="user-badges">
              <span class="badge badge-type">
                {{ getUserTypeText(userStore.userDetails.userType) }}
              </span>
            </view>
            <view class="user-actions">
              <button class="action-btn edit-btn" @click="editProfile">
                编辑资料
              </button>
              <button class="action-btn logout-btn" @click="userStore.logout">
                退出登录
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 我的数据 - 仅登录后显示 -->
    <view v-if="userStore.userDetails" class="section">
      <h3 class="section-title">
        我的数据
      </h3>
      <view class="stats-card">
        <view class="stats-grid">
          <view class="stat-item">
            <p class="stat-value stat-assessment">
              {{ userStore.userDetails.totalAssessmentCount }}
            </p>
            <p class="stat-label">
              总测评次数
            </p>
          </view>
          <view class="stat-item">
            <p class="stat-value stat-free">
              {{ userStore.userDetails.freeAssessmentCount }}
            </p>
            <p class="stat-label">
              剩余免费测评
            </p>
          </view>
          <view class="stat-item">
            <p class="stat-value stat-ai">
              {{ userStore.userDetails.totalAIInterpretationCount }}
            </p>
            <p class="stat-label">
              AI解读次数
            </p>
          </view>
          <view class="stat-item">
            <p class="stat-value stat-free-ai">
              {{ userStore.userDetails.freeAIInterpretationCount }}
            </p>
            <p class="stat-label">
              剩余AI解读
            </p>
          </view>
        </view>

        <!-- 会员状态信息 -->
        <view class="membership-card">
          <h4 class="membership-title">
            会员状态
          </h4>
          <view class="membership-info">
            <view class="membership-details">
              <p class="membership-type">
                {{ getMembershipText(userStore.userDetails.membershipType) }}
              </p>
              <p class="membership-expire">
                {{ userStore.userDetails.membershipExpireDate ? `到期时间:
                ${formatDate(userStore.userDetails.membershipExpireDate)}` : '无到期限制' }}
              </p>
            </view>
            <view v-if="userStore.userDetails.membershipType === 'free'" class="upgrade-btn" @tap="goToMembership">
              <text class="upgrade-text">
                升级会员
              </text>
            </view>
          </view>
        </view>

        <!-- 账户信息 -->
        <view class="account-info">
          <view class="account-item">
            <span class="account-label">团队权限</span>
            <view class="team-status">
              <view
                class="status-dot"
                :class="userStore.userDetails.hasTeamPermission ? 'status-active' : 'status-inactive'"
              />
              <span class="account-value">
                {{ userStore.userDetails.hasTeamPermission ? '已开启' : '未开启' }}
              </span>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请码功能 - 仅登录且有团队权限时显示 -->
    <view v-if="userStore.isLoggedIn && userStore.userDetails?.hasTeamPermission" class="section">
      <h3 class="section-title">
        我的邀请码
      </h3>
      <view class="invite-card">
        <view v-if="inviteCode" class="invite-code-container">
          <view class="invite-code">
            <text class="invite-code-value">
              {{ inviteCode }}
            </text>
            <button class="copy-btn" @tap="copyInviteCode">
              <text class="copy-icon">
                📋
              </text>
              <text class="copy-text">
                复制
              </text>
            </button>
          </view>
          <view class="invite-stats">
            <view class="invite-stat-item">
              <text class="invite-stat-value">
                {{ inviteStats.totalCount }}
              </text>
              <text class="invite-stat-label">
                已邀请
              </text>
            </view>
            <view class="invite-stat-item">
              <text class="invite-stat-value">
                {{ inviteStats.activeCount }}
              </text>
              <text class="invite-stat-label">
                活跃用户
              </text>
            </view>
            <view class="invite-stat-item">
              <text class="invite-stat-value">
                {{ inviteStats.teamCount }}
              </text>
              <text class="invite-stat-label">
                团队用户
              </text>
            </view>
          </view>
        </view>
        <view v-else-if="inviteLoading" class="invite-loading">
          <text class="loading-text">
            加载中...
          </text>
        </view>
        <view v-else class="invite-empty">
          <text class="invite-empty-text">
            点击获取您的邀请码
          </text>
          <button class="get-invite-btn" @tap="getMyInviteCode">
            获取邀请码
          </button>
        </view>
        <view class="go-invite">
          <button class="go-invite-btn" @tap="goToInvite">
            <text class="go-invite-text">
              进入邀请中心
            </text>
            <text class="go-invite-icon">
              →
            </text>
          </button>
        </view>
      </view>
    </view>
    <button @tap="goToAbout">
      关于
    </button>

    <!-- 我的成就 - 仅登录后显示 -->
    <view v-if="userStore.userDetails" class="section">
      <view class="section-header">
        <h3 class="section-title">
          我的成就
        </h3>
        <button class="view-all-btn">
          查看全部
        </button>
      </view>
      <view class="achievements-scroll">
        <view class="achievements-container">
          <!-- 新用户成就 -->
          <view v-if="userStore.userDetails.totalAssessmentCount === 0" class="achievement">
            <view class="achievement-icon achievement-new">
              <view class="i-material-symbols:star" />
            </view>
            <span class="achievement-name">新用户</span>
            <span class="achievement-badge">新</span>
          </view>

          <!-- 初级测评成就 -->
          <view v-if="userStore.userDetails.totalAssessmentCount >= 1" class="achievement">
            <view class="achievement-icon achievement-basic">
              <view class="i-material-symbols:psychology" />
            </view>
            <span class="achievement-name">初级评测</span>
          </view>

          <!-- 活跃用户成就 -->
          <view v-if="userStore.userDetails.totalAssessmentCount >= 5" class="achievement">
            <view class="achievement-icon achievement-active">
              <view class="i-material-symbols:trending-up" />
            </view>
            <span class="achievement-name">活跃用户</span>
          </view>

          <!-- AI解读达人 -->
          <view v-if="userStore.userDetails.totalAIInterpretationCount >= 3" class="achievement">
            <view class="achievement-icon achievement-ai">
              <view class="i-material-symbols:smart-toy" />
            </view>
            <span class="achievement-name">AI解读达人</span>
          </view>

          <!-- 忠实用户 -->
          <view v-if="userStore.userDetails.loginCount >= 10" class="achievement">
            <view class="achievement-icon achievement-loyal">
              <view class="i-material-symbols:emoji-events" />
            </view>
            <span class="achievement-name">忠实用户</span>
          </view>

          <!-- 会员用户 -->
          <view v-if="userStore.userDetails.membershipType !== 'free'" class="achievement">
            <view class="achievement-icon achievement-member">
              <view class="i-material-symbols:workspace-premium" />
            </view>
            <span class="achievement-name">会员用户</span>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近活动 - 仅登录后显示 -->

    <!-- 未登录时的引导信息 -->
    <view v-else class="login-guide">
      <view class="i-material-symbols:psychology login-guide-icon" />
      <h3 class="login-guide-title">
        探索您的内在世界
      </h3>
      <p class="login-guide-text">
        登录后开始您的心理健康测评之旅，了解自己，成就更好的自己
      </p>
      <button class="login-guide-btn" @click="goToLogin">
        立即开始
      </button>
    </view>
  </view>
</template>

<style lang="scss" src="./profile.css" scoped></style>
