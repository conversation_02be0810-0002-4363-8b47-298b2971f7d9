<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>

<script lang="ts" setup>
/**
 * 从 `@/utils/platform` 文件中导入 `PLATFORM` 对象。
根据上下文文件 `src/utils/platform.ts`，这个 `PLATFORM` 对象包含了平台相关的信息，比如：
- `platform`: 当前平台名称
- `isH5`: 是否为 H5 平台
- `isApp`: 是否为 App 平台
- `isMp`: 是否为小程序平台
- `isMpWeixin`: 是否为微信小程序
- `isMpAplipay`: 是否为支付宝小程序
- `isMpToutiao`: 是否为头条小程序

这个导入语句允许你在当前组件中使用这些平台判断变量，用于编写平台特定的逻辑。
使用案例
<view class="mt-4 text-center">
  当前平台是：
  <text class="text-green-500">
    {{ PLATFORM.platform }}
  </text>
</view>
 */

import { onShow } from '@dcloudio/uni-app'
import useRequest from '@/hooks/useRequest'
import { getTestAPI } from '@/service/index/test'
import { useUserStore } from '@/store/user'
import PLATFORM from '@/utils/platform'

defineOptions({
  name: 'Home',
})

// 用户store
const userStore = useUserStore()

// 网络请求相关
const { loading, error, data, run } = useRequest<string>(() => getTestAPI())

// 格式化日期函数
function formatDate(dateString: string) {
  if (!dateString)
    return '未设置'
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取问候语
function getGreeting() {
  const hour = new Date().getHours()
  if (hour < 12) {
    return '早上好'
  }
  else if (hour < 18) {
    return '下午好'
  }
  else {
    return '晚上好'
  }
}

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

function goLogin() {
  uni.navigateTo({
    url: '/pages/login/login',
  })
}

// 检查登录状态
function checkLoginStatus() {
  if (!userStore.isLoggedIn) {
    uni.showModal({
      title: '需要登录',
      content: '使用AI聊天功能需要先登录账号',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          goLogin()
        }
      },
    })
    return false
  }
  return true
}

function goStreamingTest() {
  uni.navigateTo({
    url: '/pages/test/streaming-test',
  })
}

function goInterpretation() {
  uni.navigateTo({
    url: '/pages/interpretation/interpretation',
  })
}

function handle64() {
  uni.navigateTo({
    url: '/pages/questions/questions',
  })
}

// 心理测评卡片点击处理函数
function handleAssessmentClick(type: string) {
  // 检查登录状态
  // if (!checkLoginStatus())
  //   return
  // if (!checkHasCompletedProfile())
  //   return
  if (type === '基础测评') {
    uni.navigateTo({
      url: '/pages/chat/chat?type=basic',
    })
    return
  }
  else if (type === '今日提醒') {
    uni.navigateTo({
      url: '/pages/chat/chat?type=daily',
    })
    return
  }
  else if (type === '当下状态') {
    uni.navigateTo({
      url: '/pages/chat/chat?type=current',
    })
    return
  }

  uni.showToast({
    title: `您点击了：${type}`,
    icon: 'none',
    duration: 2000,
  })
}

function goToHistory() {
  uni.navigateTo({
    url: '/pages/questions/history',
  })
}

// 进入个人主页
function goToProfile() {
  uni.navigateTo({
    url: '/pages/profile/profile',
  })
}

function testWechatPay() {
  uni.navigateTo({
    url: '/pages/payment/payment',
  })
}

function checkHasCompletedProfile() {
  if (userStore.isLoggedIn && !userStore.hasCompletedProfile) {
    uni.showModal({
      title: '完善个人信息',
      content: '请先完善您的个人信息，以便获得更准确的测评结果',
      confirmText: '去完善',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/profile/profile',
          })
        }
      },
    })
    return false
  }
  return true
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('PLATFORM', PLATFORM)
})

// 页面显示时检查用户信息
onShow(() => {
  checkHasCompletedProfile()
})

console.log('index')
</script>

<template>
  <view class="bg-white px-4 pt-2" :style="{ marginTop: `${safeAreaInsets?.top}px` }">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="app-title">
        心理健康
        <view @tap="testWechatPay()">
          测试微信支付
        </view>
        <view @tap="goToHistory()">
          历史记录
        </view>
      </view>
    </view>

    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="greeting-text">
        {{ getGreeting() }}，{{ userStore.isLoggedIn ? userStore.userDetails?.name : '开始您的心理健康之旅' }}
      </view>
      <view class="date-text">
        {{ formatDate(new Date().toISOString()) }}
      </view>
    </view>

    <!-- 心理测评快捷入口 -->
    <view class="assessment-section">
      <view class="section-title">
        测评中心a
      </view>
      <!-- 原型解读 -->
      <view class="assessment-card basic-card" @tap="goInterpretation()">
        <view class="card-icon">
          🧠
        </view>
        <view class="card-title">
          原型解读
        </view>
        <view class="card-description">
          全面评估您的心理健康基础状况
        </view>
      </view>
      <!-- 六十四心智模型测评 -->
      <view class="cards-container">
        <!-- 原型解读结束 -->

        <!-- 基础测评卡片 -->
        <view class="assessment-card basic-card" @tap="handle64()">
          <view class="card-icon">
            🧠
          </view>
          <view class="card-title">
            64心智模型测评
          </view>
          <view class="card-description">
            全面评估您的心理健康基础状况
          </view>
        </view>
        <!-- 六十四心智模型测评结束 -->
        <view class="cards-container">
          <!-- 基础测评卡片 -->
          <view class="assessment-card basic-card" @tap="handleAssessmentClick('基础测评')">
            <view class="card-icon">
              🧠
            </view>
            <view class="card-title">
              基础测评
            </view>
            <view class="card-description">
              全面评估您的心理健康基础状况
            </view>
          </view>

          <!-- 今日提醒卡片 -->
          <view class="assessment-card daily-card" @tap="handleAssessmentClick('今日提醒')">
            <view class="card-icon">
              📅
            </view>
            <view class="card-title">
              今日提醒
            </view>
            <view class="card-description">
              个性化的每日健康提醒和建议
            </view>
          </view>

          <!-- 当下状态卡片 -->
          <view class="assessment-card current-card" @tap="handleAssessmentClick('当下状态')">
            <view class="card-icon">
              💭
            </view>
            <view class="card-title">
              当下状态
            </view>
            <view class="card-description">
              分析您当前的情绪与心理状况
            </view>
          </view>
        </view>
      </view>

      <!-- 快速导入功能区域 -->
      <view class="quick-action-section">
        <view class="illustration">
          <text class="action-icon">
            📱
          </text>
        </view>
        <view class="action-title">
          了解自我，探索内心世界
        </view>
        <view class="action-subtitle">
          专业的AI心理分析，助您更好认识自己
        </view>
        <view class="start-button" @tap="goStreamingTest()">
          开始体验
        </view>
      </view>
    </view>
  </view>
</template>

<style>
@import './index.css';
</style>
