/**
 * 用户信息
 */
export interface IUserInfoVo {
  id: number
  username: string
  avatar: string
  token: string
}

/**
 * 新的用户信息结构（匹配API返回格式）
 */
export interface IUser {
  id: number
  name: string
  email: string
  role: string
  userType: string
  fullName: string | null
  phoneNumber: string
  address: string | null
  gender: string | null
  birthDate: string | null
  isActive: boolean
  profilePicture: string | null
  profileBackgroundPicture: string | null
  membershipType: string
  membershipExpireDate: string | null
  hasTeamPermission: boolean
  freeAssessmentCount: number
  freeAIInterpretationCount: number
  totalAssessmentCount: number
  totalAIInterpretationCount: number
  archetypeData: any | null
  assessmentHistory: any | null
  createdAt: string
  updatedAt: string
  lastLoginAt: string | null
  loginCount: number
  paymentHistory: any | null
  birthDateLocked: boolean
}

/**
 * 登录返回的信息
 */
export interface IUserLogin {
  id: string
  username: string
  token: string
  data: object
  user: IUser
}

/**
 * 新的登录响应格式
 */
export interface ILoginResponse {
  user: IUser
  token: string
  message: string
}

/**
 * 登录请求参数
 */
export interface ILoginRequest {
  phoneNumber: string
  password: string
}

/**
 * 获取验证码
 */
export interface ICaptcha {
  captchaEnabled: boolean
  uuid: string
  image: string
}

/**
 * 获取手机验证码
 */
export interface IGetPhoneCodeRequest {
  phoneNumber: string
}

/**
 * 上传成功的信息
 */
export interface IUploadSuccessInfo {
  fileId: number
  originalName: string
  fileName: string
  storagePath: string
  fileHash: string
  fileType: string
  fileBusinessType: string
  fileSize: number
}
/**
 * 更新用户信息
 */
export interface IUpdateInfo {
  id: number
  name: string
  sex: string
}
/**
 * 更新用户信息
 */
export interface IUpdatePassword {
  id: number
  oldPassword: string
  newPassword: string
  confirmPassword: string
}
