{
    "code": 200,
    "message": "请求成功",
    "data": "{\n  \"openapi\": \"3.0.0\",\n  \"paths\": {\n    \"/\": {\n      \"get\": {\n        \"operationId\": \"AppController_getHello\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"App\"\n        ]\n      }\n    },\n    \"/api-json\": {\n      \"get\": {\n        \"operationId\": \"AppController_getSwaggerJson\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"App\"\n        ]\n      }\n    },\n    \"/health\": {\n      \"get\": {\n        \"operationId\": \"HealthController_checkHealth\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"健康检查结果\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"database\": {\n                        \"status\": \"connected\",\n                        \"config\": {\n                          \"type\": \"mysql\",\n                          \"host\": \"localhost\",\n                          \"port\": 3306,\n                          \"database\": \"kanli\"\n                        }\n                      },\n                      \"redis\": {\n                        \"status\": \"connected\",\n                        \"config\": {\n                          \"host\": \"localhost\",\n                          \"port\": 6379,\n                          \"db\": 0\n                        }\n                      },\n                      \"overall\": \"healthy\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"检查所有服务健康状态\",\n        \"tags\": [\n          \"健康检查\"\n        ]\n      }\n    },\n    \"/health/simple\": {\n      \"get\": {\n        \"operationId\": \"HealthController_simpleHealthCheck\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"简单检查结果\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"status\": \"healthy\",\n                      \"timestamp\": \"2024-01-15T10:30:00.000Z\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"简单健康检查\",\n        \"tags\": [\n          \"健康检查\"\n        ]\n      }\n    },\n    \"/health/database\": {\n      \"get\": {\n        \"operationId\": \"HealthController_checkDatabase\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"summary\": \"检查数据库连接\",\n        \"tags\": [\n          \"健康检查\"\n        ]\n      }\n    },\n    \"/health/redis\": {\n      \"get\": {\n        \"operationId\": \"HealthController_checkRedis\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"summary\": \"检查Redis连接\",\n        \"tags\": [\n          \"健康检查\"\n        ]\n      }\n    },\n    \"/captcha/send\": {\n      \"post\": {\n        \"operationId\": \"CaptchaController_sendCaptcha\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/SendCaptchaDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"验证码发送成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"phoneNumber\": \"13800138000\",\n                      \"message\": \"验证码已发送，请注意查收\",\n                      \"expiresIn\": 300\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求参数错误或发送过于频繁\"\n          }\n        },\n        \"summary\": \"发送验证码\",\n        \"tags\": [\n          \"验证码\"\n        ]\n      }\n    },\n    \"/captcha/check-rate-limit\": {\n      \"get\": {\n        \"operationId\": \"CaptchaController_checkRateLimit\",\n        \"parameters\": [\n          {\n            \"name\": \"phoneNumber\",\n            \"required\": true,\n            \"in\": \"query\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"查询成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"isLimited\": true,\n                      \"remainingTime\": 30\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"检查发送频率限制\",\n        \"tags\": [\n          \"验证码\"\n        ]\n      }\n    },\n    \"/captcha/remaining-time\": {\n      \"get\": {\n        \"operationId\": \"CaptchaController_getRemainingTime\",\n        \"parameters\": [\n          {\n            \"name\": \"phoneNumber\",\n            \"required\": true,\n            \"in\": \"query\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"查询成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"remainingTime\": 240\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"获取验证码剩余有效时间\",\n        \"tags\": [\n          \"验证码\"\n        ]\n      }\n    },\n    \"/users/register\": {\n      \"post\": {\n        \"operationId\": \"UsersController_register\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateUserDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"注册成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"user\": {\n                        \"id\": 1,\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"phoneNumber\": \"13800138000\",\n                        \"role\": \"user\",\n                        \"userType\": \"individual\",\n                        \"isActive\": true,\n                        \"membershipType\": \"free\",\n                        \"freeAssessmentCount\": 10,\n                        \"freeAIInterpretationCount\": 10,\n                        \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n                      },\n                      \"message\": \"注册成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"注册失败，参数错误或验证码无效\"\n          }\n        },\n        \"summary\": \"注册用户\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/login\": {\n      \"post\": {\n        \"operationId\": \"UsersController_login\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/LoginUserDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"登录成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"user\": {\n                        \"id\": 1,\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"phoneNumber\": \"13800138000\",\n                        \"role\": \"user\",\n                        \"userType\": \"individual\",\n                        \"membershipType\": \"free\"\n                      },\n                      \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                      \"message\": \"登录成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"登录失败，用户不存在或密码错误\"\n          }\n        },\n        \"summary\": \"用户登录\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/wechat-login\": {\n      \"post\": {\n        \"operationId\": \"UsersController_wechatLogin\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/WechatLoginDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"微信登录成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"user\": {\n                    \"id\": 1,\n                    \"name\": \"微信用户_abc123\",\n                    \"openidWx\": \"oabc123456789\",\n                    \"role\": \"user\",\n                    \"userType\": \"individual\",\n                    \"membershipType\": \"free\",\n                    \"permissionLevel\": \"individual\",\n                    \"freeAssessmentCount\": 10,\n                    \"freeAIInterpretationCount\": 10,\n                    \"isActive\": true,\n                    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n                  },\n                  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                  \"message\": \"登录成功\",\n                  \"isNewUser\": false\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/WechatLoginResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"微信登录失败，无法获取用户信息或验证失败\"\n          }\n        },\n        \"summary\": \"微信小程序一键登录\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/decrypt-user-info\": {\n      \"post\": {\n        \"operationId\": \"UsersController_decryptUserInfo\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/DecryptUserInfoDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"解密成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"phoneNumber\": \"13800138000\",\n                  \"purePhoneNumber\": \"13800138000\",\n                  \"countryCode\": \"86\",\n                  \"watermark\": {\n                    \"timestamp\": 1477314187,\n                    \"appid\": \"wx4f4bc4dec97d474b\"\n                  }\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/DecryptedUserInfoResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"解密失败，参数错误或微信API调用失败\"\n          }\n        },\n        \"summary\": \"解密微信用户信息\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/logout\": {\n      \"post\": {\n        \"operationId\": \"UsersController_logout\",\n        \"parameters\": [\n          {\n            \"name\": \"X-JWT-Token\",\n            \"required\": true,\n            \"in\": \"header\",\n            \"description\": \"JWT Bearer token\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"退出登录成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"message\": \"退出登录成功\",\n                  \"timestamp\": \"2024-01-15T10:30:00.000Z\"\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/LogoutResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求头缺少X-JWT-Token\"\n          },\n          \"401\": {\n            \"description\": \"未授权，token无效或已过期\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"退出登录\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/profile\": {\n      \"get\": {\n        \"operationId\": \"UsersController_getProfile\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"user\": {\n                    \"id\": 1,\n                    \"name\": \"张三\",\n                    \"email\": \"<EMAIL>\",\n                    \"phoneNumber\": \"13800138000\",\n                    \"role\": \"user\",\n                    \"userType\": \"individual\",\n                    \"membershipType\": \"free\",\n                    \"freeAssessmentCount\": 8,\n                    \"freeAIInterpretationCount\": 5,\n                    \"isActive\": true,\n                    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n                  }\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/UserResponseDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权，token无效、已过期或已退出\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取用户个人信息 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/check-usage\": {\n      \"post\": {\n        \"operationId\": \"UsersController_checkUsage\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UsageCheckDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"检查成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/UsageCheckResponseDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"检查使用次数权限 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/consume-usage\": {\n      \"post\": {\n        \"operationId\": \"UsersController_consumeUsage\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UsageCheckDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"消费成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"success\": true,\n                      \"message\": \"使用成功\",\n                      \"remainingCount\": 9\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"次数不足或需要付费\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"消费使用次数 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/membership\": {\n      \"get\": {\n        \"operationId\": \"UsersController_getMembershipStatus\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/MembershipStatusDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取会员状态 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/membership/{id}\": {\n      \"patch\": {\n        \"operationId\": \"UsersController_updateMembership\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UpdateMembershipDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"更新成功\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          },\n          \"403\": {\n            \"description\": \"权限不足\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"更新会员信息 (需要团队权限)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users\": {\n      \"get\": {\n        \"operationId\": \"UsersController_findAll\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\"\n          },\n          \"403\": {\n            \"description\": \"权限不足\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取所有用户 (需要团队权限)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/users/{id}\": {\n      \"get\": {\n        \"operationId\": \"UsersController_findOne\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/UserResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"用户不存在\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"根据ID获取用户 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      },\n      \"patch\": {\n        \"operationId\": \"UsersController_update\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UpdateUserDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"更新成功\"\n          },\n          \"400\": {\n            \"description\": \"用户不存在或出生日期已锁定\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"更新用户信息 (需要登录)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      },\n      \"delete\": {\n        \"operationId\": \"UsersController_remove\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"删除成功\"\n          },\n          \"400\": {\n            \"description\": \"用户不存在\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          },\n          \"403\": {\n            \"description\": \"权限不足\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"删除用户 (需要团队权限)\",\n        \"tags\": [\n          \"用户\"\n        ]\n      }\n    },\n    \"/jwt-test/verify-token\": {\n      \"post\": {\n        \"operationId\": \"JwtTestController_verifyToken\",\n        \"parameters\": [\n          {\n            \"name\": \"X-JWT-Token\",\n            \"required\": true,\n            \"in\": \"header\",\n            \"description\": \"JWT Bearer token\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Token验证成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"valid\": true,\n                      \"payload\": {\n                        \"sub\": \"1\",\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"role\": \"user\",\n                        \"iat\": 1642248000,\n                        \"exp\": 1642251600\n                      },\n                      \"issuedAt\": \"2024-01-15T10:00:00.000Z\",\n                      \"expiresAt\": \"2024-01-15T11:00:00.000Z\",\n                      \"timeToExpire\": \"45分钟23秒\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"Token无效或已过期\"\n          }\n        },\n        \"summary\": \"验证JWT token是否有效\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/jwt-test/test-auth\": {\n      \"get\": {\n        \"operationId\": \"JwtTestController_testAuth\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"认证成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"authenticated\": true,\n                      \"user\": {\n                        \"sub\": \"1\",\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"role\": \"user\",\n                        \"iat\": 1642248000,\n                        \"exp\": 1642251600\n                      },\n                      \"message\": \"JWT认证守卫测试通过\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权，token无效或已过期\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"测试JWT认证守卫 (需要登录)\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/jwt-test/refresh-token\": {\n      \"post\": {\n        \"operationId\": \"JwtTestController_refreshToken\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Token刷新成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"oldToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                      \"newToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                      \"expiresAt\": \"2024-01-15T12:00:00.000Z\",\n                      \"message\": \"Token刷新成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"刷新JWT token (需要登录)\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/jwt-test/token-info\": {\n      \"get\": {\n        \"operationId\": \"JwtTestController_getTokenInfo\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                      \"payload\": {\n                        \"sub\": \"1\",\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"role\": \"user\",\n                        \"iat\": 1642248000,\n                        \"exp\": 1642251600\n                      },\n                      \"header\": {\n                        \"alg\": \"HS256\",\n                        \"typ\": \"JWT\"\n                      },\n                      \"issuedAt\": \"2024-01-15T10:00:00.000Z\",\n                      \"expiresAt\": \"2024-01-15T11:00:00.000Z\",\n                      \"timeToExpire\": \"45分钟23秒\",\n                      \"isBlacklisted\": false\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取当前token详细信息 (需要登录)\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/jwt-test/test-permissions\": {\n      \"get\": {\n        \"operationId\": \"JwtTestController_testPermissions\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"权限测试成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"userId\": 1,\n                      \"role\": \"user\",\n                      \"permissions\": {\n                        \"canAccessProfile\": true,\n                        \"canCreateInvite\": false,\n                        \"canMakePayment\": true,\n                        \"hasTeamPermission\": false,\n                        \"hasIndividualPermission\": true\n                      },\n                      \"userInfo\": {\n                        \"id\": 1,\n                        \"name\": \"张三\",\n                        \"email\": \"<EMAIL>\",\n                        \"membershipType\": \"free\",\n                        \"permissionLevel\": \"individual\"\n                      },\n                      \"message\": \"权限测试完成\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"测试用户权限信息 (需要登录)\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/jwt-test/generate-test-token\": {\n      \"post\": {\n        \"operationId\": \"JwtTestController_generateTestToken\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Token生成成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                      \"payload\": {\n                        \"sub\": \"999\",\n                        \"name\": \"测试用户\",\n                        \"email\": \"<EMAIL>\",\n                        \"role\": \"user\"\n                      },\n                      \"expiresAt\": \"2024-01-15T11:00:00.000Z\",\n                      \"message\": \"测试Token生成成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"参数错误\"\n          }\n        },\n        \"summary\": \"生成测试用的JWT token (仅用于开发测试)\",\n        \"tags\": [\n          \"JWT测试\"\n        ]\n      }\n    },\n    \"/cards\": {\n      \"post\": {\n        \"operationId\": \"CardsController_create\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateCardDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"Cards\"\n        ]\n      },\n      \"get\": {\n        \"operationId\": \"CardsController_findAll\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"Cards\"\n        ]\n      }\n    },\n    \"/cards/{id}\": {\n      \"get\": {\n        \"operationId\": \"CardsController_findOne\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"Cards\"\n        ]\n      },\n      \"patch\": {\n        \"operationId\": \"CardsController_update\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UpdateCardDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"Cards\"\n        ]\n      },\n      \"delete\": {\n        \"operationId\": \"CardsController_remove\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"Cards\"\n        ]\n      }\n    },\n    \"/coze/conversations\": {\n      \"post\": {\n        \"description\": \"为当前用户创建一个新的AI聊天对话会话。每个对话会话都有唯一的ID，用于后续的消息交互。\",\n        \"operationId\": \"CozeController_createConversation\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"description\": \"创建对话的请求参数\",\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateConversationDto\"\n              },\n              \"examples\": {\n                \"basic\": {\n                  \"summary\": \"基础对话创建\",\n                  \"description\": \"创建一个简单的对话会话\",\n                  \"value\": {\n                    \"title\": \"我的AI编程助手\",\n                    \"region\": \"zh\"\n                  }\n                },\n                \"withInitialMessage\": {\n                  \"summary\": \"包含初始消息的对话\",\n                  \"description\": \"创建对话并发送第一条消息\",\n                  \"value\": {\n                    \"title\": \"深度学习讨论\",\n                    \"initialMessage\": \"你好，我想了解深度学习的基础概念\",\n                    \"region\": \"zh\",\n                    \"metadata\": {\n                      \"topic\": \"machine_learning\",\n                      \"difficulty\": \"beginner\"\n                    }\n                  }\n                },\n                \"withMetadata\": {\n                  \"summary\": \"包含元数据的对话\",\n                  \"description\": \"创建带有自定义元数据的对话\",\n                  \"value\": {\n                    \"title\": \"项目代码审查\",\n                    \"region\": \"en\",\n                    \"metadata\": {\n                      \"projectName\": \"my-awesome-app\",\n                      \"language\": \"typescript\",\n                      \"reviewType\": \"security\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"对话创建成功，返回对话详细信息\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"id\": {\n                      \"type\": \"string\",\n                      \"format\": \"uuid\",\n                      \"description\": \"对话唯一标识符\"\n                    },\n                    \"title\": {\n                      \"type\": \"string\",\n                      \"description\": \"对话标题\"\n                    },\n                    \"description\": {\n                      \"type\": \"string\",\n                      \"description\": \"对话描述\"\n                    },\n                    \"userId\": {\n                      \"type\": \"string\",\n                      \"format\": \"uuid\",\n                      \"description\": \"用户ID\"\n                    },\n                    \"createdAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\",\n                      \"description\": \"创建时间\"\n                    },\n                    \"updatedAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\",\n                      \"description\": \"更新时间\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求参数错误，请检查输入数据格式\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"用户权限不足\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"创建新的AI对话会话\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      },\n      \"get\": {\n        \"description\": \"分页获取当前用户的所有AI对话会话列表，支持按创建时间倒序排列。\",\n        \"operationId\": \"CozeController_getUserConversations\",\n        \"parameters\": [\n          {\n            \"name\": \"page\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"页码，从1开始\",\n            \"schema\": {\n              \"example\": 1,\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"pageSize\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"每页显示数量，默认20条，最大100条\",\n            \"schema\": {\n              \"example\": 20,\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"对话列表获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"data\": {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"object\",\n                        \"properties\": {\n                          \"id\": {\n                            \"type\": \"string\",\n                            \"format\": \"uuid\",\n                            \"description\": \"对话唯一标识符\"\n                          },\n                          \"title\": {\n                            \"type\": \"string\",\n                            \"description\": \"对话标题\"\n                          },\n                          \"status\": {\n                            \"type\": \"string\",\n                            \"enum\": [\n                              \"active\",\n                              \"archived\",\n                              \"deleted\"\n                            ],\n                            \"description\": \"对话状态\"\n                          },\n                          \"messageCount\": {\n                            \"type\": \"number\",\n                            \"description\": \"消息总数\"\n                          },\n                          \"lastMessageAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"最后一条消息时间\"\n                          },\n                          \"lastActiveAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"最后活跃时间\"\n                          },\n                          \"createdAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"创建时间\"\n                          },\n                          \"updatedAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"更新时间\"\n                          },\n                          \"metadata\": {\n                            \"type\": \"object\",\n                            \"description\": \"对话元数据\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"total\": {\n                          \"type\": \"number\",\n                          \"description\": \"总记录数\"\n                        },\n                        \"page\": {\n                          \"type\": \"number\",\n                          \"description\": \"当前页码\"\n                        },\n                        \"pageSize\": {\n                          \"type\": \"number\",\n                          \"description\": \"每页大小\"\n                        },\n                        \"totalPages\": {\n                          \"type\": \"number\",\n                          \"description\": \"总页数\"\n                        },\n                        \"hasNext\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否有下一页\"\n                        },\n                        \"hasPrev\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否有上一页\"\n                        }\n                      }\n                    }\n                  },\n                  \"example\": {\n                    \"data\": [\n                      {\n                        \"id\": \"550e8400-e29b-41d4-a716-************\",\n                        \"title\": \"Python Web开发学习\",\n                        \"status\": \"active\",\n                        \"messageCount\": 12,\n                        \"lastMessageAt\": \"2024-01-15T14:30:00Z\",\n                        \"lastActiveAt\": \"2024-01-15T14:30:00Z\",\n                        \"createdAt\": \"2024-01-15T10:00:00Z\",\n                        \"updatedAt\": \"2024-01-15T14:30:00Z\",\n                        \"metadata\": {\n                          \"topic\": \"programming\",\n                          \"difficulty\": \"intermediate\"\n                        }\n                      },\n                      {\n                        \"id\": \"123e4567-e89b-12d3-a456-426614174000\",\n                        \"title\": \"机器学习基础讨论\",\n                        \"status\": \"archived\",\n                        \"messageCount\": 8,\n                        \"lastMessageAt\": \"2024-01-14T16:45:00Z\",\n                        \"lastActiveAt\": \"2024-01-14T16:45:00Z\",\n                        \"createdAt\": \"2024-01-14T15:00:00Z\",\n                        \"updatedAt\": \"2024-01-14T17:00:00Z\",\n                        \"metadata\": {\n                          \"topic\": \"machine_learning\",\n                          \"completed\": true\n                        }\n                      }\n                    ],\n                    \"pagination\": {\n                      \"total\": 25,\n                      \"page\": 1,\n                      \"pageSize\": 20,\n                      \"totalPages\": 2,\n                      \"hasNext\": true,\n                      \"hasPrev\": false\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"用户权限不足\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"获取用户的AI对话列表\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/coze/conversations/{id}\": {\n      \"get\": {\n        \"description\": \"根据对话ID获取对话的详细信息，包括对话的基本信息和最近的消息概览。\",\n        \"operationId\": \"CozeController_getConversation\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"对话的唯一标识符\",\n            \"schema\": {\n              \"format\": \"uuid\",\n              \"example\": \"550e8400-e29b-41d4-a716-************\",\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"对话详情获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"id\": {\n                      \"type\": \"string\",\n                      \"format\": \"uuid\"\n                    },\n                    \"title\": {\n                      \"type\": \"string\"\n                    },\n                    \"description\": {\n                      \"type\": \"string\"\n                    },\n                    \"userId\": {\n                      \"type\": \"string\",\n                      \"format\": \"uuid\"\n                    },\n                    \"messageCount\": {\n                      \"type\": \"number\"\n                    },\n                    \"lastMessageAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\"\n                    },\n                    \"createdAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\"\n                    },\n                    \"updatedAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"对话ID格式错误\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限访问该对话\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"获取指定对话的详细信息\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      },\n      \"put\": {\n        \"description\": \"更新指定对话的标题、描述等基本信息。只有对话的创建者可以更新对话信息。\",\n        \"operationId\": \"CozeController_updateConversation\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"对话的唯一标识符\",\n            \"schema\": {\n              \"format\": \"uuid\",\n              \"example\": \"550e8400-e29b-41d4-a716-************\",\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"description\": \"更新对话的请求参数\",\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/UpdateConversationDto\"\n              },\n              \"examples\": {\n                \"updateTitle\": {\n                  \"summary\": \"更新对话标题\",\n                  \"description\": \"仅更新对话的标题\",\n                  \"value\": {\n                    \"title\": \"新的对话标题 - Python Web开发\"\n                  }\n                },\n                \"updateStatus\": {\n                  \"summary\": \"更新对话状态\",\n                  \"description\": \"将对话标记为已归档\",\n                  \"value\": {\n                    \"status\": \"archived\"\n                  }\n                },\n                \"updateAll\": {\n                  \"summary\": \"更新所有可选字段\",\n                  \"description\": \"同时更新标题、状态和元数据\",\n                  \"value\": {\n                    \"title\": \"已完成的项目讨论\",\n                    \"status\": \"archived\",\n                    \"metadata\": {\n                      \"completedAt\": \"2024-01-15T10:30:00Z\",\n                      \"rating\": 5,\n                      \"tags\": [\n                        \"completed\",\n                        \"helpful\",\n                        \"programming\"\n                      ]\n                    }\n                  }\n                },\n                \"updateMetadata\": {\n                  \"summary\": \"仅更新元数据\",\n                  \"description\": \"添加或更新对话的元数据信息\",\n                  \"value\": {\n                    \"metadata\": {\n                      \"lastTopic\": \"API设计\",\n                      \"complexity\": \"intermediate\",\n                      \"estimatedDuration\": \"45分钟\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"对话更新成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"id\": {\n                      \"type\": \"string\",\n                      \"format\": \"uuid\"\n                    },\n                    \"title\": {\n                      \"type\": \"string\"\n                    },\n                    \"description\": {\n                      \"type\": \"string\"\n                    },\n                    \"updatedAt\": {\n                      \"type\": \"string\",\n                      \"format\": \"date-time\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求参数错误或对话ID格式错误\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限更新该对话\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"更新对话信息\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      },\n      \"delete\": {\n        \"description\": \"删除指定的对话及其所有相关消息。此操作不可逆，只有对话的创建者可以删除对话。\",\n        \"operationId\": \"CozeController_deleteConversation\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"要删除的对话唯一标识符\",\n            \"schema\": {\n              \"format\": \"uuid\",\n              \"example\": \"550e8400-e29b-41d4-a716-************\",\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"对话删除成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"description\": \"删除操作是否成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"对话ID格式错误\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限删除该对话\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"删除对话\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/coze/messages\": {\n      \"post\": {\n        \"description\": \"向AI发送消息并获取回复。使用标准请求-响应模式，等待AI完成回复后一次性返回完整结果。适用于短文本对话。\",\n        \"operationId\": \"CozeController_sendMessage\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"description\": \"发送消息的请求参数\",\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateMessageDto\"\n              },\n              \"examples\": {\n                \"simpleText\": {\n                  \"summary\": \"简单文本消息\",\n                  \"description\": \"发送一个基础的文本消息给AI\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"你好，请帮我解释一下什么是机器学习？\",\n                    \"type\": \"text\",\n                    \"streaming\": false\n                  }\n                },\n                \"programmingQuestion\": {\n                  \"summary\": \"编程相关问题\",\n                  \"description\": \"询问具体的编程问题\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"如何在TypeScript中实现单例模式？请提供代码示例。\",\n                    \"type\": \"text\",\n                    \"streaming\": false,\n                    \"metadata\": {\n                      \"language\": \"typescript\",\n                      \"category\": \"design_pattern\",\n                      \"difficulty\": \"intermediate\"\n                    }\n                  }\n                },\n                \"codeReview\": {\n                  \"summary\": \"代码审查请求\",\n                  \"description\": \"请求AI审查代码片段\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"请帮我审查这段代码：\\n```javascript\\nfunction fetchUser(id) {\\n  return fetch(`/api/users/${id}`).then(res => res.json());\\n}\\n```\\n有什么可以改进的地方吗？\",\n                    \"type\": \"text\",\n                    \"streaming\": false,\n                    \"metadata\": {\n                      \"requestType\": \"code_review\",\n                      \"language\": \"javascript\",\n                      \"focusAreas\": [\n                        \"error_handling\",\n                        \"best_practices\"\n                      ]\n                    }\n                  }\n                },\n                \"withObjectString\": {\n                  \"summary\": \"结构化数据消息\",\n                  \"description\": \"发送包含结构化数据的消息\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"分析这个API响应数据的结构\",\n                    \"type\": \"object_string\",\n                    \"streaming\": false,\n                    \"metadata\": {\n                      \"dataType\": \"api_response\",\n                      \"format\": \"json\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"消息发送成功，返回用户消息和AI回复的完整信息\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"userMessage\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"id\": {\n                          \"type\": \"string\",\n                          \"format\": \"uuid\",\n                          \"description\": \"用户消息ID\"\n                        },\n                        \"conversationId\": {\n                          \"type\": \"string\",\n                          \"format\": \"uuid\",\n                          \"description\": \"对话ID\"\n                        },\n                        \"content\": {\n                          \"type\": \"string\",\n                          \"description\": \"用户发送的消息内容\"\n                        },\n                        \"role\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"user\"\n                          ],\n                          \"description\": \"消息角色\"\n                        },\n                        \"type\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"text\",\n                            \"image\",\n                            \"file\",\n                            \"object_string\"\n                          ],\n                          \"description\": \"消息类型\"\n                        },\n                        \"status\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"pending\",\n                            \"completed\",\n                            \"failed\"\n                          ],\n                          \"description\": \"消息状态\"\n                        },\n                        \"metadata\": {\n                          \"type\": \"object\",\n                          \"description\": \"消息元数据\"\n                        },\n                        \"createdAt\": {\n                          \"type\": \"string\",\n                          \"format\": \"date-time\",\n                          \"description\": \"创建时间\"\n                        }\n                      }\n                    },\n                    \"aiMessage\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"id\": {\n                          \"type\": \"string\",\n                          \"format\": \"uuid\",\n                          \"description\": \"AI消息ID\"\n                        },\n                        \"conversationId\": {\n                          \"type\": \"string\",\n                          \"format\": \"uuid\",\n                          \"description\": \"对话ID\"\n                        },\n                        \"content\": {\n                          \"type\": \"string\",\n                          \"description\": \"AI回复的内容\"\n                        },\n                        \"role\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"assistant\"\n                          ],\n                          \"description\": \"消息角色\"\n                        },\n                        \"type\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"text\",\n                            \"answer\",\n                            \"function_call\",\n                            \"tool_output\"\n                          ],\n                          \"description\": \"消息类型\"\n                        },\n                        \"status\": {\n                          \"type\": \"string\",\n                          \"enum\": [\n                            \"completed\",\n                            \"failed\"\n                          ],\n                          \"description\": \"消息状态\"\n                        },\n                        \"metadata\": {\n                          \"type\": \"object\",\n                          \"description\": \"消息元数据\"\n                        },\n                        \"usage\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"promptTokens\": {\n                              \"type\": \"number\",\n                              \"description\": \"输入Token数量\"\n                            },\n                            \"completionTokens\": {\n                              \"type\": \"number\",\n                              \"description\": \"输出Token数量\"\n                            },\n                            \"totalTokens\": {\n                              \"type\": \"number\",\n                              \"description\": \"总Token数量\"\n                            }\n                          },\n                          \"description\": \"Token使用量统计\"\n                        },\n                        \"createdAt\": {\n                          \"type\": \"string\",\n                          \"format\": \"date-time\",\n                          \"description\": \"创建时间\"\n                        },\n                        \"completedAt\": {\n                          \"type\": \"string\",\n                          \"format\": \"date-time\",\n                          \"description\": \"完成时间\"\n                        }\n                      }\n                    }\n                  },\n                  \"example\": {\n                    \"userMessage\": {\n                      \"id\": \"123e4567-e89b-12d3-a456-426614174001\",\n                      \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                      \"content\": \"你好，请帮我解释一下什么是机器学习？\",\n                      \"role\": \"user\",\n                      \"type\": \"text\",\n                      \"status\": \"completed\",\n                      \"metadata\": {},\n                      \"createdAt\": \"2024-01-15T10:30:00Z\"\n                    },\n                    \"aiMessage\": {\n                      \"id\": \"123e4567-e89b-12d3-a456-426614174002\",\n                      \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                      \"content\": \"机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能...\",\n                      \"role\": \"assistant\",\n                      \"type\": \"answer\",\n                      \"status\": \"completed\",\n                      \"metadata\": {\n                        \"model\": \"gpt-3.5-turbo\",\n                        \"temperature\": 0.7\n                      },\n                      \"usage\": {\n                        \"promptTokens\": 25,\n                        \"completionTokens\": 150,\n                        \"totalTokens\": 175\n                      },\n                      \"createdAt\": \"2024-01-15T10:30:01Z\",\n                      \"completedAt\": \"2024-01-15T10:30:05Z\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求参数错误或消息内容为空\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限访问该对话或消息配额不足\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"429\": {\n            \"description\": \"请求频率过高，请稍后再试\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误或AI服务暂时不可用\"\n          }\n        },\n        \"summary\": \"发送AI消息（标准模式）\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/coze/messages/stream\": {\n      \"post\": {\n        \"description\": \"向AI发送消息并以Server-Sent Events (SSE)方式实时流式接收回复。适用于长文本生成，用户可以实时看到AI的回复过程。\",\n        \"operationId\": \"CozeController_streamMessage\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"description\": \"发送流式消息的请求参数\",\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateMessageDto\"\n              },\n              \"examples\": {\n                \"longFormContent\": {\n                  \"summary\": \"长文本生成请求\",\n                  \"description\": \"请求AI生成较长的内容，适合流式输出\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"请详细介绍一下深度学习的发展历程，包括关键里程碑、重要人物和技术突破\",\n                    \"type\": \"text\",\n                    \"streaming\": true,\n                    \"metadata\": {\n                      \"expectedLength\": \"long\",\n                      \"topic\": \"deep_learning_history\"\n                    }\n                  }\n                },\n                \"codeGeneration\": {\n                  \"summary\": \"代码生成请求\",\n                  \"description\": \"请求AI生成较复杂的代码，实时查看生成过程\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"请帮我写一个完整的React组件，实现一个带搜索、分页和排序功能的用户列表。要求使用TypeScript和React Hooks。\",\n                    \"type\": \"text\",\n                    \"streaming\": true,\n                    \"metadata\": {\n                      \"language\": \"typescript\",\n                      \"framework\": \"react\",\n                      \"complexity\": \"high\",\n                      \"features\": [\n                        \"search\",\n                        \"pagination\",\n                        \"sorting\"\n                      ]\n                    }\n                  }\n                },\n                \"tutorialExplanation\": {\n                  \"summary\": \"教程式解释\",\n                  \"description\": \"请求详细的分步骤解释，适合流式展示\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"请一步一步地教我如何从零开始构建一个RESTful API，使用Node.js和Express框架\",\n                    \"type\": \"text\",\n                    \"streaming\": true,\n                    \"metadata\": {\n                      \"instructionType\": \"step_by_step\",\n                      \"technology\": \"nodejs_express\",\n                      \"audience\": \"beginner\"\n                    }\n                  }\n                },\n                \"documentAnalysis\": {\n                  \"summary\": \"文档分析请求\",\n                  \"description\": \"分析复杂文档或代码，逐步输出分析结果\",\n                  \"value\": {\n                    \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                    \"content\": \"请分析这个复杂的SQL查询性能问题，并提供优化建议：\\nSELECT u.*, p.*, c.count FROM users u LEFT JOIN profiles p ON u.id = p.user_id LEFT JOIN (SELECT user_id, COUNT(*) as count FROM comments GROUP BY user_id) c ON u.id = c.user_id WHERE u.created_at > '2023-01-01' ORDER BY u.last_login_at DESC\",\n                    \"type\": \"text\",\n                    \"streaming\": true,\n                    \"metadata\": {\n                      \"analysisType\": \"sql_performance\",\n                      \"database\": \"postgresql\",\n                      \"requestType\": \"optimization\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"流式消息发送成功，返回SSE数据流\",\n            \"headers\": {\n              \"Content-Type\": {\n                \"description\": \"text/event-stream\"\n              },\n              \"Cache-Control\": {\n                \"description\": \"no-cache\"\n              },\n              \"Connection\": {\n                \"description\": \"keep-alive\"\n              }\n            },\n            \"content\": {\n              \"text/event-stream\": {\n                \"schema\": {\n                  \"type\": \"string\",\n                  \"description\": \"Server-Sent Events格式的数据流，每个事件都是JSON格式\",\n                  \"examples\": {\n                    \"streamEvents\": {\n                      \"summary\": \"流式事件示例\",\n                      \"description\": \"展示完整的流式响应过程中的各种事件类型\",\n                      \"value\": \"data: {\\\"event\\\":\\\"start\\\",\\\"data\\\":{\\\"messageId\\\":\\\"123e4567-e89b-12d3-a456-426614174003\\\",\\\"conversationId\\\":\\\"550e8400-e29b-41d4-a716-************\\\"}}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"深度学习\\\"}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"是人工智能\\\"}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"的一个重要分支\\\"}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"，它通过算法\\\"}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"让计算机从数据中学习\\\"}\\n\\ndata: {\\\"event\\\":\\\"done\\\",\\\"data\\\":{\\\"messageId\\\":\\\"123e4567-e89b-12d3-a456-426614174003\\\",\\\"status\\\":\\\"completed\\\",\\\"usage\\\":{\\\"promptTokens\\\":32,\\\"completionTokens\\\":245,\\\"totalTokens\\\":277},\\\"completedAt\\\":\\\"2024-01-15T10:30:15Z\\\"}}\\n\\n\"\n                    },\n                    \"errorEvent\": {\n                      \"summary\": \"错误事件示例\",\n                      \"description\": \"当流式处理过程中发生错误时的响应格式\",\n                      \"value\": \"data: {\\\"event\\\":\\\"start\\\",\\\"data\\\":{\\\"messageId\\\":\\\"123e4567-e89b-12d3-a456-426614174004\\\",\\\"conversationId\\\":\\\"550e8400-e29b-41d4-a716-************\\\"}}\\n\\ndata: {\\\"event\\\":\\\"chunk\\\",\\\"data\\\":\\\"正在处理您的请求\\\"}\\n\\ndata: {\\\"event\\\":\\\"error\\\",\\\"data\\\":{\\\"error\\\":\\\"AI服务暂时不可用\\\",\\\"code\\\":\\\"AI_SERVICE_UNAVAILABLE\\\",\\\"messageId\\\":\\\"123e4567-e89b-12d3-a456-426614174004\\\"}}\\n\\n\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"请求参数错误或消息内容为空\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限访问该对话或消息配额不足\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"429\": {\n            \"description\": \"请求频率过高，请稍后再试\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误或AI服务暂时不可用\"\n          }\n        },\n        \"summary\": \"发送AI消息（流式模式）\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/coze/conversations/{id}/messages\": {\n      \"get\": {\n        \"description\": \"分页获取指定对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。\",\n        \"operationId\": \"CozeController_getConversationMessages\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"对话的唯一标识符\",\n            \"schema\": {\n              \"format\": \"uuid\",\n              \"example\": \"550e8400-e29b-41d4-a716-************\",\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"page\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"页码，从1开始\",\n            \"schema\": {\n              \"example\": 1,\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"pageSize\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"每页显示数量，默认50条，最大200条\",\n            \"schema\": {\n              \"example\": 50,\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"消息列表获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"data\": {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"object\",\n                        \"properties\": {\n                          \"id\": {\n                            \"type\": \"string\",\n                            \"format\": \"uuid\",\n                            \"description\": \"消息唯一标识符\"\n                          },\n                          \"conversationId\": {\n                            \"type\": \"string\",\n                            \"format\": \"uuid\",\n                            \"description\": \"所属对话ID\"\n                          },\n                          \"content\": {\n                            \"type\": \"string\",\n                            \"description\": \"消息内容\"\n                          },\n                          \"role\": {\n                            \"type\": \"string\",\n                            \"enum\": [\n                              \"user\",\n                              \"assistant\",\n                              \"system\"\n                            ],\n                            \"description\": \"消息角色\"\n                          },\n                          \"type\": {\n                            \"type\": \"string\",\n                            \"enum\": [\n                              \"text\",\n                              \"image\",\n                              \"file\",\n                              \"object_string\",\n                              \"answer\",\n                              \"function_call\",\n                              \"tool_output\"\n                            ],\n                            \"description\": \"消息类型\"\n                          },\n                          \"status\": {\n                            \"type\": \"string\",\n                            \"enum\": [\n                              \"pending\",\n                              \"completed\",\n                              \"failed\"\n                            ],\n                            \"description\": \"消息状态\"\n                          },\n                          \"metadata\": {\n                            \"type\": \"object\",\n                            \"description\": \"消息元数据\"\n                          },\n                          \"usage\": {\n                            \"type\": \"object\",\n                            \"properties\": {\n                              \"promptTokens\": {\n                                \"type\": \"number\",\n                                \"description\": \"输入Token数量\"\n                              },\n                              \"completionTokens\": {\n                                \"type\": \"number\",\n                                \"description\": \"输出Token数量\"\n                              },\n                              \"totalTokens\": {\n                                \"type\": \"number\",\n                                \"description\": \"总Token数量\"\n                              }\n                            },\n                            \"description\": \"Token使用量统计（仅AI消息有此字段）\"\n                          },\n                          \"createdAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"创建时间\"\n                          },\n                          \"completedAt\": {\n                            \"type\": \"string\",\n                            \"format\": \"date-time\",\n                            \"description\": \"完成时间（仅已完成消息有此字段）\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"total\": {\n                          \"type\": \"number\",\n                          \"description\": \"总消息数\"\n                        },\n                        \"page\": {\n                          \"type\": \"number\",\n                          \"description\": \"当前页码\"\n                        },\n                        \"pageSize\": {\n                          \"type\": \"number\",\n                          \"description\": \"每页大小\"\n                        },\n                        \"totalPages\": {\n                          \"type\": \"number\",\n                          \"description\": \"总页数\"\n                        },\n                        \"hasNext\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否有下一页\"\n                        },\n                        \"hasPrev\": {\n                          \"type\": \"boolean\",\n                          \"description\": \"是否有上一页\"\n                        }\n                      }\n                    }\n                  },\n                  \"example\": {\n                    \"data\": [\n                      {\n                        \"id\": \"456e7890-e89b-12d3-a456-426614174005\",\n                        \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                        \"content\": \"机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能。它通过算法分析数据，识别模式，并做出预测或决策。\",\n                        \"role\": \"assistant\",\n                        \"type\": \"answer\",\n                        \"status\": \"completed\",\n                        \"metadata\": {\n                          \"model\": \"gpt-3.5-turbo\",\n                          \"temperature\": 0.7\n                        },\n                        \"usage\": {\n                          \"promptTokens\": 25,\n                          \"completionTokens\": 67,\n                          \"totalTokens\": 92\n                        },\n                        \"createdAt\": \"2024-01-15T10:30:01Z\",\n                        \"completedAt\": \"2024-01-15T10:30:03Z\"\n                      },\n                      {\n                        \"id\": \"789e0123-e89b-12d3-a456-426614174004\",\n                        \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n                        \"content\": \"你好，请帮我解释一下什么是机器学习？\",\n                        \"role\": \"user\",\n                        \"type\": \"text\",\n                        \"status\": \"completed\",\n                        \"metadata\": {},\n                        \"createdAt\": \"2024-01-15T10:30:00Z\"\n                      }\n                    ],\n                    \"pagination\": {\n                      \"total\": 24,\n                      \"page\": 1,\n                      \"pageSize\": 50,\n                      \"totalPages\": 1,\n                      \"hasNext\": false,\n                      \"hasPrev\": false\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"对话ID格式错误或分页参数错误\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限访问该对话的消息\"\n          },\n          \"404\": {\n            \"description\": \"对话不存在\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"获取对话的消息历史\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/coze/conversations/{conversationId}/chats/{chatId}\": {\n      \"delete\": {\n        \"description\": \"取消当前正在进行的AI对话请求。用于停止长时间运行的AI生成任务，特别是流式对话。\",\n        \"operationId\": \"CozeController_cancelChat\",\n        \"parameters\": [\n          {\n            \"name\": \"conversationId\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"对话的唯一标识符\",\n            \"schema\": {\n              \"format\": \"uuid\",\n              \"example\": \"550e8400-e29b-41d4-a716-************\",\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"chatId\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"description\": \"聊天会话的标识符，用于标识特定的AI对话请求\",\n            \"schema\": {\n              \"example\": \"chat_1234567890\",\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"聊天取消成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"description\": \"取消操作是否成功\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"对话ID或聊天ID格式错误\"\n          },\n          \"401\": {\n            \"description\": \"用户未认证，请先登录\"\n          },\n          \"403\": {\n            \"description\": \"无权限取消该聊天\"\n          },\n          \"404\": {\n            \"description\": \"对话或聊天不存在\"\n          },\n          \"410\": {\n            \"description\": \"聊天已完成，无法取消\"\n          },\n          \"500\": {\n            \"description\": \"服务器内部错误\"\n          }\n        },\n        \"summary\": \"取消正在进行的AI对话\",\n        \"tags\": [\n          \"AI聊天管理\"\n        ]\n      }\n    },\n    \"/team-invite/my-invite-code\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteController_getMyInviteCode\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"inviteCode\": \"ABC12345\",\n                      \"inviteLink\": \"http://localhost:3000/invite/ABC12345\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          },\n          \"403\": {\n            \"description\": \"没有邀请权限\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取我的永久邀请码\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/check/{inviteCode}\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteController_checkInviteCode\",\n        \"parameters\": [\n          {\n            \"name\": \"inviteCode\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"检查成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"valid\": true,\n                      \"inviterName\": \"张三\",\n                      \"inviterId\": 1\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"检查邀请码是否有效\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/accept\": {\n      \"post\": {\n        \"operationId\": \"TeamInviteController_acceptInvite\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/AcceptInviteDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"邀请接受成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"success\": true,\n                      \"message\": \"邀请接受成功，您已获得团队权限\",\n                      \"teamPermissionGranted\": true\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"邀请码无效或已过期\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"接受邀请\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/my-info\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteController_getMyInviteInfo\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"myInviteCode\": \"ABC12345\",\n                  \"inviteCount\": 5,\n                  \"canInviteOthers\": true,\n                  \"stats\": {\n                    \"totalInvites\": 5,\n                    \"successfulInvites\": 5,\n                    \"pendingInvites\": 0,\n                    \"expiredInvites\": 0,\n                    \"successRate\": 100\n                  },\n                  \"sentInvites\": []\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/MyInviteInfoDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取我的邀请信息\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/my-invited-users\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteController_getMyInvitedUsers\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"schema\": {\n              \"example\": {\n                \"code\": 200,\n                \"message\": \"请求成功\",\n                \"data\": {\n                  \"myInviteCode\": \"ABC12345\",\n                  \"totalCount\": 3,\n                  \"invitedUsers\": [\n                    {\n                      \"id\": 2,\n                      \"name\": \"李四\",\n                      \"email\": \"<EMAIL>\",\n                      \"phoneNumber\": \"13800138001\",\n                      \"joinedAt\": \"2024-01-15T10:30:00.000Z\",\n                      \"permissionLevel\": \"team\",\n                      \"userType\": \"individual\",\n                      \"membershipType\": \"free\"\n                    },\n                    {\n                      \"id\": 3,\n                      \"name\": \"王五\",\n                      \"email\": \"<EMAIL>\",\n                      \"phoneNumber\": \"13800138002\",\n                      \"joinedAt\": \"2024-01-16T14:20:00.000Z\",\n                      \"permissionLevel\": \"team\",\n                      \"userType\": \"individual\",\n                      \"membershipType\": \"basic\"\n                    }\n                  ],\n                  \"summary\": {\n                    \"totalUsers\": 3,\n                    \"activeUsers\": 3,\n                    \"teamUsers\": 3,\n                    \"individualUsers\": 0\n                  }\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/MyInvitedUsersDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取使用我邀请码的用户详细信息\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/my-invited-users-count\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteController_getMyInvitedUsersCount\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"myInviteCode\": \"ABC12345\",\n                      \"totalCount\": 5,\n                      \"activeCount\": 4,\n                      \"teamCount\": 3\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取使用我邀请码的用户数量统计\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite\": {\n      \"post\": {\n        \"deprecated\": true,\n        \"description\": \"该API已废弃，请使用 GET /my-invite-code 获取永久邀请码\",\n        \"operationId\": \"TeamInviteController_createInvite\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateInviteDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"返回用户的永久邀请码\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/InviteResponseDto\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          },\n          \"403\": {\n            \"description\": \"没有邀请权限\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"创建邀请码（已废弃）\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/detail/{id}\": {\n      \"get\": {\n        \"deprecated\": true,\n        \"description\": \"该API已废弃，永久邀请码系统不再需要此功能\",\n        \"operationId\": \"TeamInviteController_getInviteDetail\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"400\": {\n            \"description\": \"该功能已废弃\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取邀请记录详情（已废弃）\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/{id}\": {\n      \"delete\": {\n        \"deprecated\": true,\n        \"description\": \"该API已废弃，永久邀请码无法取消\",\n        \"operationId\": \"TeamInviteController_cancelInvite\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"400\": {\n            \"description\": \"永久邀请码无法取消\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"取消邀请（已废弃）\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/team-invite/cleanup-expired\": {\n      \"post\": {\n        \"deprecated\": true,\n        \"description\": \"该API已废弃，永久邀请码不会过期\",\n        \"operationId\": \"TeamInviteController_cleanupExpiredInvites\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"永久邀请码不会过期\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"count\": 0\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"清理过期邀请（已废弃）\",\n        \"tags\": [\n          \"团队邀请\"\n        ]\n      }\n    },\n    \"/invite-stats/overview\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteStatsController_getOverview\",\n        \"parameters\": [\n          {\n            \"name\": \"timeRange\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"时间范围\",\n            \"schema\": {\n              \"default\": \"30d\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"7d\",\n                \"30d\",\n                \"90d\",\n                \"custom\"\n              ]\n            }\n          },\n          {\n            \"name\": \"startDate\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"开始日期（自定义时间范围时使用）\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"endDate\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"结束日期（自定义时间范围时使用）\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"sortBy\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"排序方式\",\n            \"schema\": {\n              \"default\": \"usage\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"usage\",\n                \"lastActive\",\n                \"joinDate\"\n              ]\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"限制返回数量\",\n            \"schema\": {\n              \"minimum\": 1,\n              \"maximum\": 100,\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"totalInvitedUsers\": 15,\n                      \"activeUsers\": 12,\n                      \"avgUsagePerUser\": 25.6,\n                      \"activeRate\": 80,\n                      \"totalUsage\": 384,\n                      \"growthRate\": 15.5,\n                      \"timeRange\": \"30d\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取邀请用户统计概览\",\n        \"tags\": [\n          \"邀请统计\"\n        ]\n      }\n    },\n    \"/invite-stats/usage-trend\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteStatsController_getUsageTrend\",\n        \"parameters\": [\n          {\n            \"name\": \"timeRange\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"时间范围\",\n            \"schema\": {\n              \"default\": \"30d\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"7d\",\n                \"30d\",\n                \"90d\"\n              ]\n            }\n          },\n          {\n            \"name\": \"chartType\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"图表类型\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\n                \"line\",\n                \"bar\"\n              ]\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"chartData\": {\n                        \"xAxis\": {\n                          \"type\": \"category\",\n                          \"data\": [\n                            \"2024-01-01\",\n                            \"2024-01-02\"\n                          ]\n                        },\n                        \"yAxis\": {\n                          \"type\": \"value\",\n                          \"name\": \"使用次数\"\n                        },\n                        \"series\": [\n                          {\n                            \"name\": \"总使用次数\",\n                            \"type\": \"line\",\n                            \"data\": [\n                              25,\n                              30\n                            ]\n                          },\n                          {\n                            \"name\": \"活跃用户数\",\n                            \"type\": \"line\",\n                            \"data\": [\n                              8,\n                              10\n                            ]\n                          }\n                        ]\n                      },\n                      \"overview\": {\n                        \"totalInvitedUsers\": 15,\n                        \"activeUsers\": 12,\n                        \"avgUsagePerUser\": 25.6,\n                        \"activeRate\": 80,\n                        \"totalUsage\": 384,\n                        \"growthRate\": 15.5,\n                        \"timeRange\": \"30d\"\n                      },\n                      \"timeRange\": \"30d\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取使用趋势数据（ECharts格式）\",\n        \"tags\": [\n          \"邀请统计\"\n        ]\n      }\n    },\n    \"/invite-stats/user-ranking\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteStatsController_getUserRanking\",\n        \"parameters\": [\n          {\n            \"name\": \"limit\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"限制返回数量\",\n            \"schema\": {\n              \"minimum\": 1,\n              \"maximum\": 50,\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"sortBy\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"排序方式\",\n            \"schema\": {\n              \"default\": \"usage\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"usage\",\n                \"lastActive\",\n                \"joinDate\"\n              ]\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"chartData\": {\n                        \"xAxis\": {\n                          \"type\": \"category\",\n                          \"data\": [\n                            \"用户A\",\n                            \"用户B\",\n                            \"用户C\"\n                          ]\n                        },\n                        \"yAxis\": {\n                          \"type\": \"value\",\n                          \"name\": \"使用次数\"\n                        },\n                        \"series\": [\n                          {\n                            \"name\": \"使用次数\",\n                            \"type\": \"bar\",\n                            \"data\": [\n                              45,\n                              38,\n                              32\n                            ]\n                          }\n                        ]\n                      },\n                      \"userList\": [\n                        {\n                          \"userId\": 1,\n                          \"userName\": \"用户A\",\n                          \"userEmail\": \"<EMAIL>\",\n                          \"rank\": 1,\n                          \"usageCount\": 45,\n                          \"lastActiveDate\": \"2024-01-15T10:30:00.000Z\",\n                          \"joinDate\": \"2024-01-01T00:00:00.000Z\"\n                        }\n                      ],\n                      \"totalUsers\": 10\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取用户活跃度排名（ECharts格式）\",\n        \"tags\": [\n          \"邀请统计\"\n        ]\n      }\n    },\n    \"/invite-stats/daily-activity\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteStatsController_getDailyActivity\",\n        \"parameters\": [\n          {\n            \"name\": \"timeRange\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"时间范围\",\n            \"schema\": {\n              \"default\": \"30d\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"30d\",\n                \"90d\"\n              ]\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"chartData\": {\n                        \"tooltip\": {\n                          \"position\": \"top\"\n                        },\n                        \"xAxis\": {\n                          \"type\": \"category\",\n                          \"data\": [\n                            \"周一\",\n                            \"周二\",\n                            \"周三\"\n                          ]\n                        },\n                        \"yAxis\": {\n                          \"type\": \"category\",\n                          \"data\": [\n                            \"第1周\",\n                            \"第2周\"\n                          ]\n                        },\n                        \"visualMap\": {\n                          \"min\": 0,\n                          \"max\": 20\n                        },\n                        \"series\": [\n                          {\n                            \"name\": \"活跃度\",\n                            \"type\": \"heatmap\",\n                            \"data\": [\n                              [\n                                0,\n                                0,\n                                15\n                              ],\n                              [\n                                1,\n                                0,\n                                12\n                              ]\n                            ]\n                          }\n                        ]\n                      },\n                      \"dailyData\": [\n                        {\n                          \"date\": \"2024-01-01\",\n                          \"value\": 15,\n                          \"usageCount\": 25\n                        },\n                        {\n                          \"date\": \"2024-01-02\",\n                          \"value\": 12,\n                          \"usageCount\": 20\n                        }\n                      ],\n                      \"timeRange\": \"30d\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取每日活跃度热力图数据（ECharts格式）\",\n        \"tags\": [\n          \"邀请统计\"\n        ]\n      }\n    },\n    \"/invite-stats/feature-usage\": {\n      \"get\": {\n        \"operationId\": \"TeamInviteStatsController_getFeatureUsage\",\n        \"parameters\": [\n          {\n            \"name\": \"timeRange\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"时间范围\",\n            \"schema\": {\n              \"default\": \"30d\",\n              \"type\": \"string\",\n              \"enum\": [\n                \"7d\",\n                \"30d\",\n                \"90d\"\n              ]\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"chartData\": {\n                        \"tooltip\": {\n                          \"trigger\": \"item\"\n                        },\n                        \"legend\": {\n                          \"data\": [\n                            \"AI解读\",\n                            \"评估查询\"\n                          ]\n                        },\n                        \"series\": [\n                          {\n                            \"name\": \"功能使用\",\n                            \"type\": \"pie\",\n                            \"radius\": \"50%\",\n                            \"data\": [\n                              {\n                                \"name\": \"AI解读\",\n                                \"value\": 45\n                              },\n                              {\n                                \"name\": \"评估查询\",\n                                \"value\": 35\n                              }\n                            ]\n                          }\n                        ]\n                      },\n                      \"featureList\": [\n                        {\n                          \"name\": \"AI解读\",\n                          \"value\": 45,\n                          \"percentage\": 45\n                        },\n                        {\n                          \"name\": \"评估查询\",\n                          \"value\": 35,\n                          \"percentage\": 35\n                        }\n                      ],\n                      \"totalUsage\": 100\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取功能使用分布数据（ECharts格式）\",\n        \"tags\": [\n          \"邀请统计\"\n        ]\n      }\n    },\n    \"/payment\": {\n      \"post\": {\n        \"operationId\": \"PaymentController_createPayment\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreatePaymentDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"支付订单创建成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"payment\": {\n                        \"id\": 1,\n                        \"orderNo\": \"KL1642234567890ABC\",\n                        \"userId\": 1,\n                        \"type\": \"team_permission\",\n                        \"method\": \"wechat_pay\",\n                        \"amount\": 9900,\n                        \"status\": \"pending\",\n                        \"description\": \"开通团队权限\",\n                        \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n                      },\n                      \"paymentData\": {\n                        \"orderNo\": \"KL1642234567890ABC\",\n                        \"amount\": 9900,\n                        \"description\": \"开通团队权限\",\n                        \"qrCodeUrl\": \"https://api.example.com/wechat/qr/KL1642234567890ABC\",\n                        \"expireTime\": \"2024-01-15T11:00:00.000Z\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"支付参数错误\"\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"创建支付订单\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/payment/callback/{orderNo}\": {\n      \"post\": {\n        \"operationId\": \"PaymentController_handlePaymentCallback\",\n        \"parameters\": [\n          {\n            \"name\": \"orderNo\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"回调处理成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"success\": true,\n                    \"message\": \"支付成功\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"支付回调接口（第三方支付平台调用）\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/payment/wechat/pre-payment\": {\n      \"post\": {\n        \"operationId\": \"PaymentController_prePayment\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/WechatPaymentDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"预支付成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"timeStamp\": \"1642234567\",\n                      \"nonceStr\": \"random_string\",\n                      \"package\": \"prepay_id=wx123456789\",\n                      \"signType\": \"RSA\",\n                      \"paySign\": \"signature\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"支付参数错误\"\n          },\n          \"404\": {\n            \"description\": \"用户或订单不存在\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"微信支付预支付接口\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/payment/wechat/notify\": {\n      \"post\": {\n        \"operationId\": \"PaymentController_handleWechatPayNotify\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/WxNotifyDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"回调处理成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": \"SUCCESS\",\n                    \"message\": \"成功\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"微信支付回调接口\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/payment/history\": {\n      \"get\": {\n        \"operationId\": \"PaymentController_getPaymentHistory\",\n        \"parameters\": [\n          {\n            \"name\": \"page\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"页码\",\n            \"schema\": {\n              \"example\": 1,\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"每页数量\",\n            \"schema\": {\n              \"example\": 10,\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"code\": 200,\n                    \"message\": \"请求成功\",\n                    \"data\": {\n                      \"payments\": [],\n                      \"total\": 0,\n                      \"page\": 1,\n                      \"limit\": 10\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取支付历史记录\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/payment/detail/{id}\": {\n      \"get\": {\n        \"operationId\": \"PaymentController_getPaymentDetail\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/Payment\"\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"description\": \"未授权\"\n          },\n          \"404\": {\n            \"description\": \"支付记录不存在\"\n          }\n        },\n        \"security\": [\n          {\n            \"bearer\": []\n          }\n        ],\n        \"summary\": \"获取支付详情\",\n        \"tags\": [\n          \"支付\"\n        ]\n      }\n    },\n    \"/query-templates\": {\n      \"post\": {\n        \"operationId\": \"QueryTemplateController_create\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateQueryTemplateDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"QueryTemplate\"\n        ]\n      },\n      \"get\": {\n        \"operationId\": \"QueryTemplateController_findAll\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"QueryTemplate\"\n        ]\n      }\n    },\n    \"/query-templates/{id}\": {\n      \"get\": {\n        \"operationId\": \"QueryTemplateController_findOne\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"QueryTemplate\"\n        ]\n      },\n      \"put\": {\n        \"operationId\": \"QueryTemplateController_update\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"QueryTemplate\"\n        ]\n      },\n      \"delete\": {\n        \"operationId\": \"QueryTemplateController_remove\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"QueryTemplate\"\n        ]\n      }\n    },\n    \"/date-query/prototype\": {\n      \"post\": {\n        \"operationId\": \"DateQueryController_queryByDate\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/DateQueryDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"DateQuery\"\n        ]\n      }\n    },\n    \"/date-query/initialize\": {\n      \"post\": {\n        \"operationId\": \"DateQueryController_initializeData\",\n        \"parameters\": [],\n        \"responses\": {\n          \"201\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"DateQuery\"\n        ]\n      }\n    },\n    \"/date-query/all\": {\n      \"get\": {\n        \"operationId\": \"DateQueryController_getAllData\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"DateQuery\"\n        ]\n      }\n    },\n    \"/questions\": {\n      \"get\": {\n        \"operationId\": \"QuestionController_findAll\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"题目管理\"\n        ]\n      },\n      \"post\": {\n        \"operationId\": \"QuestionController_create\",\n        \"parameters\": [],\n        \"responses\": {\n          \"201\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/{id}\": {\n      \"get\": {\n        \"operationId\": \"QuestionController_getQuestionById\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"题目管理\"\n        ]\n      },\n      \"delete\": {\n        \"operationId\": \"QuestionController_remove\",\n        \"parameters\": [\n          {\n            \"name\": \"id\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"\"\n          }\n        },\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/calculate-results\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_calculateResults\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CalculateResultDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"计算结果成功\",\n            \"schema\": {\n              \"example\": {\n                \"mbti\": {\n                  \"E\": 5,\n                  \"I\": 3,\n                  \"S\": 4,\n                  \"N\": 4,\n                  \"T\": 6,\n                  \"F\": 2,\n                  \"J\": 3,\n                  \"P\": 5\n                },\n                \"disc\": {\n                  \"D\": 8,\n                  \"I\": 6,\n                  \"S\": 10,\n                  \"C\": 8\n                },\n                \"mbtiType\": \"ENFP\",\n                \"discType\": \"S\",\n                \"finalType\": \"ENFP-S\",\n                \"dimensionScores\": {\n                  \"能量互动\": 5,\n                  \"信息感知\": 4,\n                  \"决策形成\": 6,\n                  \"行动实施\": 5\n                }\n              }\n            },\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/CalculateResultResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"答案数组长度不正确或题目数量不正确\"\n          }\n        },\n        \"summary\": \"计算MBTI和DISC结果\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/personality-results\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_createPersonalityResult\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreatePersonalityResultDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"创建成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/PersonalityResultResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"该人格类型已存在\"\n          }\n        },\n        \"summary\": \"创建人格测试结果\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      },\n      \"get\": {\n        \"operationId\": \"QuestionController_getAllPersonalityResults\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"array\",\n                  \"items\": {\n                    \"$ref\": \"#/components/schemas/PersonalityResultResponseDto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"获取所有人格测试结果\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/personality-results/get\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_getPersonalityResult\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/GetPersonalityResultDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/PersonalityResultResponseDto\"\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"description\": \"未找到该人格类型的结果\"\n          }\n        },\n        \"summary\": \"根据类型代码获取人格测试结果\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/personality-results/seed\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_seedPersonalityResults\",\n        \"parameters\": [],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"批量创建成功\"\n          }\n        },\n        \"summary\": \"批量创建人格测试结果数据\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/personality-results/calculate\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_getPersonalityResultByCalculation\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CalculateResultDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/PersonalityResultResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"答案数组长度不正确或题目数量不正确\"\n          },\n          \"404\": {\n            \"description\": \"未找到对应的人格类型结果\"\n          }\n        },\n        \"summary\": \"根据答题结果获取对应的人格测试结果\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/user-answer-records\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_saveUserAnswerRecord\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/CreateUserAnswerRecordDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"保存成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/UserAnswerRecordResponseDto\"\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"答案数组长度不正确或题目数量不正确\"\n          }\n        },\n        \"summary\": \"保存用户答题记录\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/user-answer-records/history\": {\n      \"get\": {\n        \"operationId\": \"QuestionController_getUserAnswerRecords\",\n        \"parameters\": [\n          {\n            \"name\": \"pageSize\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"每页数量，默认为10，最大100\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"page\",\n            \"required\": false,\n            \"in\": \"query\",\n            \"description\": \"页码，默认为1\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          },\n          {\n            \"name\": \"userId\",\n            \"required\": true,\n            \"in\": \"query\",\n            \"description\": \"用户ID\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/PaginatedUserAnswerRecordsResponseDto\"\n                }\n              }\n            }\n          }\n        },\n        \"summary\": \"获取用户的答题记录历史\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/user-answer-records/current-type\": {\n      \"post\": {\n        \"operationId\": \"QuestionController_getUserCurrentType\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"$ref\": \"#/components/schemas/GetUserCurrentTypeDto\"\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"$ref\": \"#/components/schemas/UserCurrentTypeResponseDto\"\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"description\": \"用户暂无答题记录\"\n          }\n        },\n        \"summary\": \"获取用户的当前人格类型\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/questions/user-answer-records/proto/{userId}\": {\n      \"get\": {\n        \"operationId\": \"QuestionController_getUserProtoType\",\n        \"parameters\": [\n          {\n            \"name\": \"userId\",\n            \"required\": true,\n            \"in\": \"path\",\n            \"schema\": {\n              \"type\": \"number\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"获取成功\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"example\": {\n                    \"proto\": \"ISTJ-D\"\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"description\": \"用户暂无答题记录\"\n          }\n        },\n        \"summary\": \"获取用户的proto类型（当前类型）\",\n        \"tags\": [\n          \"题目管理\"\n        ]\n      }\n    },\n    \"/uploads/upload\": {\n      \"post\": {\n        \"operationId\": \"UploadController_uploadFile\",\n        \"parameters\": [],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"multipart/form-data\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"file\": {\n                    \"type\": \"string\",\n                    \"format\": \"binary\",\n                    \"description\": \"要上传的文件\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"上传成功\"\n          },\n          \"400\": {\n            \"description\": \"上传失败\"\n          }\n        },\n        \"summary\": \"上传文件\",\n        \"tags\": [\n          \"上传\"\n        ]\n      }\n    }\n  },\n  \"info\": {\n    \"title\": \"KanLi API\",\n    \"description\": \"KanLi API Documentation\",\n    \"version\": \"1.0\",\n    \"contact\": {}\n  },\n  \"tags\": [\n    {\n      \"name\": \"kanli\",\n      \"description\": \"\"\n    }\n  ],\n  \"servers\": [],\n  \"components\": {\n    \"schemas\": {\n      \"SendCaptchaDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"phoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"手机号\",\n            \"example\": \"13800138000\",\n            \"pattern\": \"^1[3-9]\\\\d{9}$\"\n          }\n        },\n        \"required\": [\n          \"phoneNumber\"\n        ]\n      },\n      \"CreateUserDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"phoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"手机号（可选）\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"用户名\",\n            \"example\": \"张三\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"邮箱（可选）\",\n            \"example\": \"<EMAIL>\"\n          },\n          \"password\": {\n            \"type\": \"string\",\n            \"description\": \"密码\",\n            \"example\": \"Password123!\"\n          },\n          \"captcha\": {\n            \"type\": \"string\",\n            \"description\": \"验证码（当提供手机号时必填）\"\n          },\n          \"fullName\": {\n            \"type\": \"string\",\n            \"description\": \"真实姓名（可选）\"\n          },\n          \"address\": {\n            \"type\": \"string\",\n            \"description\": \"地址（可选）\"\n          },\n          \"gender\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"male\",\n              \"female\",\n              \"other\"\n            ],\n            \"description\": \"性别（可选）\",\n            \"example\": \"male\"\n          },\n          \"birthDate\": {\n            \"type\": \"string\",\n            \"description\": \"出生日期（可选）\",\n            \"format\": \"date\",\n            \"example\": \"1990-01-01\"\n          },\n          \"userType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"individual\",\n              \"institution\"\n            ],\n            \"description\": \"用户类型\",\n            \"default\": \"individual\",\n            \"example\": \"individual\"\n          },\n          \"profilePicture\": {\n            \"type\": \"string\",\n            \"description\": \"头像链接（可选）\"\n          },\n          \"profileBackgroundPicture\": {\n            \"type\": \"string\",\n            \"description\": \"背景图链接（可选）\"\n          }\n        },\n        \"required\": [\n          \"name\",\n          \"password\",\n          \"userType\"\n        ]\n      },\n      \"LoginUserDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"phoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"手机号\",\n            \"example\": \"13800138000\"\n          },\n          \"password\": {\n            \"type\": \"string\",\n            \"description\": \"密码\",\n            \"example\": \"Password123!\"\n          }\n        },\n        \"required\": [\n          \"phoneNumber\",\n          \"password\"\n        ]\n      },\n      \"WechatLoginDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"code\": {\n            \"type\": \"string\",\n            \"description\": \"微信小程序登录凭证code\",\n            \"example\": \"061FGqIV0K0XcX1wKfIF0X8w8y0FGqIt\"\n          }\n        },\n        \"required\": [\n          \"code\"\n        ]\n      },\n      \"WechatLoginResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"user\": {\n            \"type\": \"object\",\n            \"description\": \"用户信息\"\n          },\n          \"token\": {\n            \"type\": \"string\",\n            \"description\": \"JWT令牌\"\n          },\n          \"message\": {\n            \"type\": \"string\",\n            \"description\": \"响应消息\"\n          },\n          \"isNewUser\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否为新用户\"\n          }\n        },\n        \"required\": [\n          \"user\",\n          \"token\",\n          \"message\",\n          \"isNewUser\"\n        ]\n      },\n      \"DecryptUserInfoDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"encryptedData\": {\n            \"type\": \"string\",\n            \"description\": \"加密的用户数据\",\n            \"example\": \"2Jguq6v771RzmIg824V8mAD+CYBjIoABP9geaQF8qGeHHzzZlyWsIDl6L8BQGtmISAD2JbdfPw/TUVBPse1XnhWQVA+8FvCnoVfjAex58RPOtf7PG0699gHYCaxoS5sXN7xBz75fEpmH2YWFc9Xo+31p8z6k2YBN1dLPStvqfcz1Z1w5cofvXAFbEubbjuRfiRl9JKzHnXOtQxiVICyPOg==\"\n          },\n          \"iv\": {\n            \"type\": \"string\",\n            \"description\": \"初始向量\",\n            \"example\": \"kmneH1ouMGszcWXgEqkfhA==\"\n          },\n          \"code\": {\n            \"type\": \"string\",\n            \"description\": \"微信登录凭证\",\n            \"example\": \"4a0fbec7c332c9e20c04910b287c0183da74ced7bd089b3657a724363f201d67\"\n          },\n          \"dataType\": {\n            \"type\": \"string\",\n            \"description\": \"数据类型（phoneNumber或userInfo）\",\n            \"example\": \"phoneNumber\"\n          }\n        },\n        \"required\": [\n          \"encryptedData\",\n          \"iv\",\n          \"code\"\n        ]\n      },\n      \"DecryptedUserInfoResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"phoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"手机号（仅当dataType为phoneNumber时返回）\",\n            \"example\": \"13800138000\"\n          },\n          \"purePhoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"纯手机号（仅当dataType为phoneNumber时返回）\",\n            \"example\": \"13800138000\"\n          },\n          \"countryCode\": {\n            \"type\": \"string\",\n            \"description\": \"国家代码（仅当dataType为phoneNumber时返回）\",\n            \"example\": \"86\"\n          },\n          \"nickName\": {\n            \"type\": \"string\",\n            \"description\": \"昵称（仅当dataType为userInfo时返回）\",\n            \"example\": \"张三\"\n          },\n          \"gender\": {\n            \"type\": \"number\",\n            \"description\": \"性别（仅当dataType为userInfo时返回）\",\n            \"example\": 1\n          },\n          \"language\": {\n            \"type\": \"string\",\n            \"description\": \"语言（仅当dataType为userInfo时返回）\",\n            \"example\": \"zh_CN\"\n          },\n          \"city\": {\n            \"type\": \"string\",\n            \"description\": \"城市（仅当dataType为userInfo时返回）\",\n            \"example\": \"Guangzhou\"\n          },\n          \"province\": {\n            \"type\": \"string\",\n            \"description\": \"省份（仅当dataType为userInfo时返回）\",\n            \"example\": \"Guangdong\"\n          },\n          \"country\": {\n            \"type\": \"string\",\n            \"description\": \"国家（仅当dataType为userInfo时返回）\",\n            \"example\": \"CN\"\n          },\n          \"avatarUrl\": {\n            \"type\": \"string\",\n            \"description\": \"头像URL（仅当dataType为userInfo时返回）\",\n            \"example\": \"http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0\"\n          },\n          \"unionId\": {\n            \"type\": \"string\",\n            \"description\": \"微信开放平台唯一标识（仅当dataType为userInfo时返回）\",\n            \"example\": \"ocMvos6NjeKLIBqg5Mr9QjxrP1FA\"\n          },\n          \"watermark\": {\n            \"type\": \"object\",\n            \"description\": \"水印信息\",\n            \"example\": {\n              \"timestamp\": 1477314187,\n              \"appid\": \"wx4f4bc4dec97d474b\"\n            }\n          }\n        },\n        \"required\": [\n          \"watermark\"\n        ]\n      },\n      \"LogoutResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"message\": {\n            \"type\": \"string\",\n            \"description\": \"退出登录消息\"\n          },\n          \"timestamp\": {\n            \"type\": \"string\",\n            \"description\": \"退出时间\"\n          }\n        },\n        \"required\": [\n          \"message\",\n          \"timestamp\"\n        ]\n      },\n      \"UserResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"number\",\n            \"description\": \"用户唯一标识\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"用户名（昵称）\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"用户邮箱（唯一）\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"admin\",\n              \"user\"\n            ],\n            \"description\": \"用户角色\"\n          },\n          \"userType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"individual\",\n              \"institution\"\n            ],\n            \"description\": \"用户类型：个人或机构\"\n          },\n          \"fullName\": {\n            \"type\": \"string\",\n            \"description\": \"真实姓名（可选）\"\n          },\n          \"phoneNumber\": {\n            \"type\": \"string\",\n            \"description\": \"手机号（唯一）\"\n          },\n          \"address\": {\n            \"type\": \"string\",\n            \"description\": \"地址（可选）\"\n          },\n          \"gender\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"male\",\n              \"female\",\n              \"other\"\n            ],\n            \"description\": \"性别（可选）\"\n          },\n          \"birthDate\": {\n            \"type\": \"string\",\n            \"description\": \"出生日期（可选）\",\n            \"format\": \"date\"\n          },\n          \"isActive\": {\n            \"type\": \"boolean\",\n            \"description\": \"账号是否激活\"\n          },\n          \"profilePicture\": {\n            \"type\": \"string\",\n            \"description\": \"头像链接（可选）\"\n          },\n          \"profileBackgroundPicture\": {\n            \"type\": \"string\",\n            \"description\": \"背景图链接（可选）\"\n          },\n          \"membershipType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"free\",\n              \"basic\",\n              \"premium\",\n              \"team\"\n            ],\n            \"description\": \"会员类型\"\n          },\n          \"membershipExpireDate\": {\n            \"type\": \"string\",\n            \"description\": \"会员过期时间（可选）\",\n            \"format\": \"date-time\"\n          },\n          \"hasTeamPermission\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否拥有团队权限\"\n          },\n          \"freeAssessmentCount\": {\n            \"type\": \"number\",\n            \"description\": \"免费评估次数\"\n          },\n          \"freeAIInterpretationCount\": {\n            \"type\": \"number\",\n            \"description\": \"免费 AI 解读次数\"\n          },\n          \"totalAssessmentCount\": {\n            \"type\": \"number\",\n            \"description\": \"总评估次数\"\n          },\n          \"totalAIInterpretationCount\": {\n            \"type\": \"number\",\n            \"description\": \"总 AI 解读次数\"\n          },\n          \"lastLoginAt\": {\n            \"type\": \"string\",\n            \"description\": \"上次登录时间\",\n            \"format\": \"date-time\"\n          },\n          \"loginCount\": {\n            \"type\": \"number\",\n            \"description\": \"登录次数\"\n          },\n          \"birthDateLocked\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否锁定出生日期\"\n          },\n          \"createdAt\": {\n            \"type\": \"string\",\n            \"description\": \"创建时间\",\n            \"format\": \"date-time\"\n          },\n          \"updatedAt\": {\n            \"type\": \"string\",\n            \"description\": \"更新时间\",\n            \"format\": \"date-time\"\n          }\n        },\n        \"required\": [\n          \"id\",\n          \"name\",\n          \"email\",\n          \"role\",\n          \"userType\",\n          \"phoneNumber\",\n          \"isActive\",\n          \"membershipType\",\n          \"hasTeamPermission\",\n          \"freeAssessmentCount\",\n          \"freeAIInterpretationCount\",\n          \"totalAssessmentCount\",\n          \"totalAIInterpretationCount\",\n          \"loginCount\",\n          \"birthDateLocked\",\n          \"createdAt\",\n          \"updatedAt\"\n        ]\n      },\n      \"UsageCheckDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"assessment\",\n              \"aiInterpretation\"\n            ],\n            \"description\": \"使用类型：评估或AI解读\",\n            \"example\": \"assessment\"\n          }\n        },\n        \"required\": [\n          \"type\"\n        ]\n      },\n      \"UsageCheckResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"canUse\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否可以使用（true表示可以免费使用或会员权限，false表示需要付费）\"\n          },\n          \"remainingFreeCount\": {\n            \"type\": \"number\",\n            \"description\": \"剩余免费次数\"\n          },\n          \"isMember\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否为会员\"\n          },\n          \"membershipExpireDate\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"会员过期时间（如果是会员）\"\n          },\n          \"message\": {\n            \"type\": \"string\",\n            \"description\": \"提示信息\"\n          }\n        },\n        \"required\": [\n          \"canUse\",\n          \"remainingFreeCount\",\n          \"isMember\",\n          \"message\"\n        ]\n      },\n      \"MembershipStatusDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"membershipType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"free\",\n              \"basic\",\n              \"premium\",\n              \"team\"\n            ],\n            \"description\": \"会员类型\"\n          },\n          \"membershipExpireDate\": {\n            \"type\": \"string\",\n            \"description\": \"会员过期时间\",\n            \"format\": \"date-time\"\n          },\n          \"hasTeamPermission\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否拥有团队权限\"\n          },\n          \"isActive\": {\n            \"type\": \"boolean\",\n            \"description\": \"会员是否有效\"\n          },\n          \"remainingDays\": {\n            \"type\": \"number\",\n            \"description\": \"剩余天数（如果会员有效）\"\n          }\n        },\n        \"required\": [\n          \"membershipType\",\n          \"hasTeamPermission\",\n          \"isActive\"\n        ]\n      },\n      \"UpdateMembershipDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"membershipType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"free\",\n              \"basic\",\n              \"premium\",\n              \"team\"\n            ],\n            \"description\": \"会员类型\"\n          },\n          \"membershipExpireDate\": {\n            \"type\": \"string\",\n            \"description\": \"会员过期时间\",\n            \"format\": \"date-time\",\n            \"example\": \"2024-12-31T23:59:59.000Z\"\n          },\n          \"hasTeamPermission\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否拥有团队权限（可选）\"\n          }\n        },\n        \"required\": [\n          \"membershipType\",\n          \"membershipExpireDate\"\n        ]\n      },\n      \"UpdateUserDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"membershipType\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"free\",\n              \"basic\",\n              \"premium\",\n              \"team\"\n            ],\n            \"description\": \"会员类型（可选）\"\n          },\n          \"membershipExpireDate\": {\n            \"type\": \"string\",\n            \"description\": \"会员过期时间（可选）\",\n            \"format\": \"date-time\"\n          },\n          \"hasTeamPermission\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否拥有团队权限（可选）\"\n          },\n          \"isActive\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否激活账号（可选）\"\n          },\n          \"birthDateLocked\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否锁定出生日期（可选）\"\n          },\n          \"birthCalendarType\": {\n            \"type\": \"string\",\n            \"description\": \"生日是公历还是阴历（可选）\"\n          }\n        }\n      },\n      \"CreateCardDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"UpdateCardDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"CreateConversationDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"title\": {\n            \"type\": \"string\",\n            \"description\": \"对话标题\"\n          },\n          \"initialMessage\": {\n            \"type\": \"string\",\n            \"description\": \"初始消息\"\n          },\n          \"region\": {\n            \"type\": \"string\",\n            \"description\": \"使用的区域\",\n            \"default\": \"zh\"\n          },\n          \"metadata\": {\n            \"type\": \"object\",\n            \"description\": \"对话元数据\"\n          }\n        },\n        \"required\": [\n          \"title\",\n          \"region\"\n        ]\n      },\n      \"UpdateConversationDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"title\": {\n            \"type\": \"string\",\n            \"description\": \"对话标题\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"active\",\n              \"archived\",\n              \"deleted\"\n            ],\n            \"description\": \"对话状态\"\n          },\n          \"metadata\": {\n            \"type\": \"object\",\n            \"description\": \"对话元数据\"\n          }\n        }\n      },\n      \"CreateMessageDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"conversationId\": {\n            \"type\": \"string\",\n            \"description\": \"对话ID\"\n          },\n          \"content\": {\n            \"type\": \"string\",\n            \"description\": \"消息内容\"\n          },\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"text\",\n              \"image\",\n              \"file\",\n              \"object_string\",\n              \"answer\",\n              \"function_call\",\n              \"tool_output\"\n            ],\n            \"description\": \"消息类型\",\n            \"default\": \"text\"\n          },\n          \"streaming\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否使用流式响应\",\n            \"default\": false\n          },\n          \"metadata\": {\n            \"type\": \"object\",\n            \"description\": \"消息元数据\"\n          }\n        },\n        \"required\": [\n          \"conversationId\",\n          \"content\",\n          \"type\",\n          \"streaming\"\n        ]\n      },\n      \"AcceptInviteDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"inviteCode\": {\n            \"type\": \"string\",\n            \"description\": \"邀请码\",\n            \"example\": \"ABC12345\",\n            \"minLength\": 8,\n            \"maxLength\": 32\n          }\n        },\n        \"required\": [\n          \"inviteCode\"\n        ]\n      },\n      \"InviteStatsDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"totalInvites\": {\n            \"type\": \"number\",\n            \"description\": \"总邀请数\"\n          },\n          \"successfulInvites\": {\n            \"type\": \"number\",\n            \"description\": \"成功邀请数\"\n          },\n          \"pendingInvites\": {\n            \"type\": \"number\",\n            \"description\": \"待接受邀请数\"\n          },\n          \"expiredInvites\": {\n            \"type\": \"number\",\n            \"description\": \"已过期邀请数\"\n          },\n          \"successRate\": {\n            \"type\": \"number\",\n            \"description\": \"邀请成功率（百分比）\"\n          }\n        },\n        \"required\": [\n          \"totalInvites\",\n          \"successfulInvites\",\n          \"pendingInvites\",\n          \"expiredInvites\",\n          \"successRate\"\n        ]\n      },\n      \"MyInviteInfoDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"myInviteCode\": {\n            \"type\": \"string\",\n            \"description\": \"我的邀请码\"\n          },\n          \"inviteCount\": {\n            \"type\": \"number\",\n            \"description\": \"邀请人数\"\n          },\n          \"stats\": {\n            \"description\": \"邀请统计\",\n            \"allOf\": [\n              {\n                \"$ref\": \"#/components/schemas/InviteStatsDto\"\n              }\n            ]\n          },\n          \"canInviteOthers\": {\n            \"type\": \"boolean\",\n            \"description\": \"是否可以邀请他人\"\n          },\n          \"sentInvites\": {\n            \"description\": \"我发送的邀请列表\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"required\": [\n          \"myInviteCode\",\n          \"inviteCount\",\n          \"stats\",\n          \"canInviteOthers\",\n          \"sentInvites\"\n        ]\n      },\n      \"MyInvitedUsersDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"myInviteCode\": {\n            \"type\": \"string\",\n            \"description\": \"我的邀请码\"\n          },\n          \"totalCount\": {\n            \"type\": \"number\",\n            \"description\": \"总邀请人数\"\n          },\n          \"invitedUsers\": {\n            \"description\": \"使用我邀请码的用户列表\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            }\n          },\n          \"summary\": {\n            \"type\": \"object\",\n            \"description\": \"统计信息\"\n          }\n        },\n        \"required\": [\n          \"myInviteCode\",\n          \"totalCount\",\n          \"invitedUsers\",\n          \"summary\"\n        ]\n      },\n      \"CreateInviteDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"direct\",\n              \"link\"\n            ],\n            \"description\": \"邀请类型（已废弃：永久邀请码统一为DIRECT类型）\",\n            \"default\": \"direct\",\n            \"example\": \"direct\",\n            \"deprecated\": true\n          },\n          \"expiresAt\": {\n            \"type\": \"string\",\n            \"description\": \"邀请过期时间（已废弃：永久邀请码不会过期）\",\n            \"example\": \"2024-12-31T23:59:59.000Z\",\n            \"deprecated\": true\n          },\n          \"note\": {\n            \"type\": \"string\",\n            \"description\": \"邀请备注\",\n            \"example\": \"邀请同事加入团队\"\n          },\n          \"validHours\": {\n            \"type\": \"number\",\n            \"description\": \"邀请链接有效期（已废弃：永久邀请码不会过期）\",\n            \"example\": 24,\n            \"minimum\": 1,\n            \"maximum\": 168,\n            \"deprecated\": true\n          }\n        },\n        \"required\": [\n          \"type\"\n        ]\n      },\n      \"InviteResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"number\",\n            \"description\": \"邀请记录ID\"\n          },\n          \"inviteCode\": {\n            \"type\": \"string\",\n            \"description\": \"邀请码\"\n          },\n          \"inviterId\": {\n            \"type\": \"number\",\n            \"description\": \"邀请人ID\"\n          },\n          \"inviterName\": {\n            \"type\": \"string\",\n            \"description\": \"邀请人姓名\"\n          },\n          \"inviteeId\": {\n            \"type\": \"number\",\n            \"description\": \"被邀请人ID\"\n          },\n          \"inviteeName\": {\n            \"type\": \"string\",\n            \"description\": \"被邀请人姓名\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"pending\",\n              \"accepted\",\n              \"expired\",\n              \"cancelled\"\n            ],\n            \"description\": \"邀请状态\"\n          },\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"direct\",\n              \"link\"\n            ],\n            \"description\": \"邀请类型\"\n          },\n          \"inviteLink\": {\n            \"type\": \"string\",\n            \"description\": \"邀请链接\"\n          },\n          \"expiresAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"过期时间\"\n          },\n          \"acceptedAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"接受时间\"\n          },\n          \"note\": {\n            \"type\": \"string\",\n            \"description\": \"备注\"\n          },\n          \"createdAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"创建时间\"\n          },\n          \"updatedAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"更新时间\"\n          }\n        },\n        \"required\": [\n          \"id\",\n          \"inviteCode\",\n          \"inviterId\",\n          \"inviterName\",\n          \"status\",\n          \"type\",\n          \"createdAt\",\n          \"updatedAt\"\n        ]\n      },\n      \"CreatePaymentDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"team_permission\",\n              \"assessment_credits\",\n              \"ai_interpretation_credits\"\n            ],\n            \"description\": \"支付类型\",\n            \"example\": \"team_permission\"\n          },\n          \"method\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"wechat_pay\",\n              \"alipay\"\n            ],\n            \"description\": \"支付方式\",\n            \"example\": \"wechat_pay\"\n          },\n          \"amount\": {\n            \"type\": \"number\",\n            \"description\": \"支付金额（分）\",\n            \"example\": 9900,\n            \"minimum\": 1\n          },\n          \"quantity\": {\n            \"type\": \"number\",\n            \"description\": \"购买数量（购买次数时使用）\",\n            \"example\": 10,\n            \"minimum\": 1\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"支付描述\",\n            \"example\": \"开通团队权限\"\n          },\n          \"clientIp\": {\n            \"type\": \"string\",\n            \"description\": \"客户端IP地址\",\n            \"example\": \"127.0.0.1\"\n          }\n        },\n        \"required\": [\n          \"type\",\n          \"method\",\n          \"amount\"\n        ]\n      },\n      \"WechatPaymentDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"appid\": {\n            \"type\": \"string\",\n            \"description\": \"小程序appid\"\n          },\n          \"payer\": {\n            \"type\": \"string\",\n            \"description\": \"微信账号的uuid\"\n          },\n          \"openid\": {\n            \"type\": \"string\",\n            \"description\": \"微信账号的openid\"\n          },\n          \"amount\": {\n            \"type\": \"string\",\n            \"description\": \"支付金额（元）\"\n          },\n          \"orderNo\": {\n            \"type\": \"string\",\n            \"description\": \"订单编号\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"支付描述\"\n          }\n        },\n        \"required\": [\n          \"appid\",\n          \"payer\",\n          \"openid\",\n          \"amount\",\n          \"orderNo\",\n          \"description\"\n        ]\n      },\n      \"WxNotifyDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"string\",\n            \"description\": \"通知ID\"\n          },\n          \"create_time\": {\n            \"type\": \"string\",\n            \"description\": \"创建时间\"\n          },\n          \"event_type\": {\n            \"type\": \"string\",\n            \"description\": \"通知类型\"\n          },\n          \"resource_type\": {\n            \"type\": \"string\",\n            \"description\": \"通知数据类型\"\n          },\n          \"resource\": {\n            \"type\": \"object\",\n            \"description\": \"通知数据\"\n          },\n          \"summary\": {\n            \"type\": \"string\",\n            \"description\": \"摘要\"\n          }\n        },\n        \"required\": [\n          \"id\",\n          \"create_time\",\n          \"event_type\",\n          \"resource_type\",\n          \"resource\"\n        ]\n      },\n      \"Payment\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"number\",\n            \"description\": \"支付记录ID\"\n          },\n          \"orderNo\": {\n            \"type\": \"string\",\n            \"description\": \"订单号\"\n          },\n          \"userId\": {\n            \"type\": \"number\",\n            \"description\": \"用户ID\"\n          },\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"team_permission\",\n              \"assessment_credits\",\n              \"ai_interpretation_credits\"\n            ],\n            \"description\": \"支付类型\"\n          },\n          \"method\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"wechat_pay\",\n              \"alipay\"\n            ],\n            \"description\": \"支付方式\"\n          },\n          \"amount\": {\n            \"type\": \"number\",\n            \"description\": \"支付金额（分）\"\n          },\n          \"quantity\": {\n            \"type\": \"number\",\n            \"description\": \"购买数量\",\n            \"default\": 1\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\n              \"pending\",\n              \"processing\",\n              \"success\",\n              \"failed\",\n              \"cancelled\",\n              \"refunded\"\n            ],\n            \"description\": \"支付状态\",\n            \"default\": \"pending\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"支付描述\"\n          },\n          \"thirdPartyTransactionId\": {\n            \"type\": \"string\",\n            \"description\": \"第三方支付平台交易号\"\n          },\n          \"paidAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"支付完成时间\"\n          },\n          \"refundedAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"退款时间\"\n          },\n          \"refundAmount\": {\n            \"type\": \"number\",\n            \"description\": \"退款金额（分）\"\n          },\n          \"failureReason\": {\n            \"type\": \"string\",\n            \"description\": \"失败原因\"\n          },\n          \"thirdPartyResponse\": {\n            \"type\": \"object\",\n            \"description\": \"第三方支付响应数据\"\n          },\n          \"metadata\": {\n            \"type\": \"object\",\n            \"description\": \"支付扩展信息\"\n          },\n          \"createdAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"创建时间\"\n          },\n          \"updatedAt\": {\n            \"format\": \"date-time\",\n            \"type\": \"string\",\n            \"description\": \"更新时间\"\n          }\n        },\n        \"required\": [\n          \"id\",\n          \"orderNo\",\n          \"userId\",\n          \"type\",\n          \"method\",\n          \"amount\",\n          \"quantity\",\n          \"status\",\n          \"createdAt\",\n          \"updatedAt\"\n        ]\n      },\n      \"CreateQueryTemplateDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"DateQueryDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"CalculateResultDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"answers\": {\n            \"description\": \"用户答案数组，索引对应题目ID，值为选项索引（0-3）\",\n            \"example\": [\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3,\n              0,\n              1,\n              2,\n              3\n            ],\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"number\"\n            }\n          }\n        },\n        \"required\": [\n          \"answers\"\n        ]\n      },\n      \"MbtiResultDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"E\": {\n            \"type\": \"number\",\n            \"description\": \"外向-内向维度\",\n            \"example\": {\n              \"E\": 5,\n              \"I\": 3\n            }\n          },\n          \"I\": {\n            \"type\": \"number\",\n            \"description\": \"内向-外向维度\",\n            \"example\": {\n              \"E\": 5,\n              \"I\": 3\n            }\n          },\n          \"S\": {\n            \"type\": \"number\",\n            \"description\": \"感觉-直觉维度\",\n            \"example\": {\n              \"S\": 4,\n              \"N\": 4\n            }\n          },\n          \"N\": {\n            \"type\": \"number\",\n            \"description\": \"直觉-感觉维度\",\n            \"example\": {\n              \"S\": 4,\n              \"N\": 4\n            }\n          },\n          \"T\": {\n            \"type\": \"number\",\n            \"description\": \"思考-情感维度\",\n            \"example\": {\n              \"T\": 6,\n              \"F\": 2\n            }\n          },\n          \"F\": {\n            \"type\": \"number\",\n            \"description\": \"情感-思考维度\",\n            \"example\": {\n              \"T\": 6,\n              \"F\": 2\n            }\n          },\n          \"J\": {\n            \"type\": \"number\",\n            \"description\": \"判断-知觉维度\",\n            \"example\": {\n              \"J\": 3,\n              \"P\": 5\n            }\n          },\n          \"P\": {\n            \"type\": \"number\",\n            \"description\": \"知觉-判断维度\",\n            \"example\": {\n              \"J\": 3,\n              \"P\": 5\n            }\n          }\n        },\n        \"required\": [\n          \"E\",\n          \"I\",\n          \"S\",\n          \"N\",\n          \"T\",\n          \"F\",\n          \"J\",\n          \"P\"\n        ]\n      },\n      \"DiscResultDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"D\": {\n            \"type\": \"number\",\n            \"description\": \"支配型\",\n            \"example\": 8\n          },\n          \"I\": {\n            \"type\": \"number\",\n            \"description\": \"影响型\",\n            \"example\": 6\n          },\n          \"S\": {\n            \"type\": \"number\",\n            \"description\": \"稳健型\",\n            \"example\": 10\n          },\n          \"C\": {\n            \"type\": \"number\",\n            \"description\": \"谨慎型\",\n            \"example\": 8\n          }\n        },\n        \"required\": [\n          \"D\",\n          \"I\",\n          \"S\",\n          \"C\"\n        ]\n      },\n      \"CalculateResultResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"mbti\": {\n            \"description\": \"MBTI各维度得分\",\n            \"allOf\": [\n              {\n                \"$ref\": \"#/components/schemas/MbtiResultDto\"\n              }\n            ]\n          },\n          \"disc\": {\n            \"description\": \"DISC各维度得分\",\n            \"allOf\": [\n              {\n                \"$ref\": \"#/components/schemas/DiscResultDto\"\n              }\n            ]\n          },\n          \"mbtiType\": {\n            \"type\": \"string\",\n            \"description\": \"MBTI类型\",\n            \"example\": \"ENFP\"\n          },\n          \"discType\": {\n            \"type\": \"string\",\n            \"description\": \"DISC主导风格\",\n            \"example\": \"S\"\n          },\n          \"finalType\": {\n            \"type\": \"string\",\n            \"description\": \"最终组合类型\",\n            \"example\": \"ENFP-S\"\n          },\n          \"dimensionScores\": {\n            \"type\": \"object\",\n            \"description\": \"各维度详细得分\"\n          }\n        },\n        \"required\": [\n          \"mbti\",\n          \"disc\",\n          \"mbtiType\",\n          \"discType\",\n          \"finalType\",\n          \"dimensionScores\"\n        ]\n      },\n      \"CreatePersonalityResultDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"PersonalityResultResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"GetPersonalityResultDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"CreateUserAnswerRecordDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"UserAnswerRecordResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"PaginatedUserAnswerRecordsResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"GetUserCurrentTypeDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      },\n      \"UserCurrentTypeResponseDto\": {\n        \"type\": \"object\",\n        \"properties\": {}\n      }\n    }\n  }\n}",
    "timestamp": "2025-07-22T09:54:50.484Z"
    }
    
    JavaScript
    当前节点：$.code