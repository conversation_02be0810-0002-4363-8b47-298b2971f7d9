<route lang="json5">
{
  style: {
    navigationBarTitleText: '会话管理测试',
  },
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRealChat } from '@/composables/use-real-chat'

defineOptions({
  name: 'SessionTestPage',
})

const {
  sessions,
  currentSessionId,
  isLoading,
  error,
  initializeSessions,
  createNewSession,
  switchSession,
  deleteSession,
  refreshSessions,
  clearError,
} = useRealChat()

const testResult = ref('')
const isTesting = ref(false)

// 测试会话管理功能
async function testSessionManagement() {
  isTesting.value = true
  testResult.value = '开始测试会话管理功能...\n'

  try {
    // 1. 初始化会话
    testResult.value += '1. 初始化会话...\n'
    await initializeSessions()
    testResult.value += `   成功！当前会话数: ${sessions.value.length}\n`

    // 2. 创建新会话
    testResult.value += '2. 创建新会话...\n'
    await createNewSession()
    testResult.value += `   成功！会话数: ${sessions.value.length}\n`

    // 3. 切换会话
    if (sessions.value.length > 1) {
      testResult.value += '3. 切换会话...\n'
      const targetSessionId = sessions.value[1].id
      switchSession(targetSessionId)
      testResult.value += `   成功！当前会话ID: ${currentSessionId.value}\n`
    }

    // 4. 刷新会话列表
    testResult.value += '4. 刷新会话列表...\n'
    await refreshSessions()
    testResult.value += `   成功！会话数: ${sessions.value.length}\n`

    testResult.value += '\n✅ 所有测试通过！会话管理功能正常工作。\n'
  }
  catch (error) {
    console.error('测试失败:', error)
    testResult.value += `\n❌ 测试失败: ${error}\n`
  }
  finally {
    isTesting.value = false
  }
}

// 测试删除会话
async function testDeleteSession() {
  if (sessions.value.length === 0) {
    testResult.value = '没有可删除的会话'
    return
  }

  isTesting.value = true
  testResult.value = '测试删除会话...\n'

  try {
    const sessionToDelete = sessions.value[0]
    const originalCount = sessions.value.length

    testResult.value += `删除会话: ${sessionToDelete.title}\n`
    await deleteSession(sessionToDelete.id)

    testResult.value += `删除成功！会话数从 ${originalCount} 减少到 ${sessions.value.length}\n`
  }
  catch (error) {
    console.error('删除测试失败:', error)
    testResult.value += `删除失败: ${error}\n`
  }
  finally {
    isTesting.value = false
  }
}

// 显示会话信息
function showSessionInfo() {
  testResult.value = '当前会话信息:\n\n'

  if (sessions.value.length === 0) {
    testResult.value += '暂无会话\n'
    return
  }

  sessions.value.forEach((session, index) => {
    testResult.value += `${index + 1}. ${session.title}\n`
    testResult.value += `   ID: ${session.id}\n`
    testResult.value += `   消息数: ${session.messages.length}\n`
    testResult.value += `   创建时间: ${new Date(session.createTime).toLocaleString()}\n`
    testResult.value += `   当前会话: ${session.id === currentSessionId.value ? '是' : '否'}\n\n`
  })
}

onMounted(async () => {
  try {
    await initializeSessions()
  }
  catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<template>
  <view class="session-test-container">
    <view class="header">
      <text class="title">
        会话管理功能测试
      </text>
    </view>

    <view class="controls">
      <view class="btn primary" :class="{ disabled: isTesting }" @tap="testSessionManagement">
        <text class="btn-text">
          测试会话管理
        </text>
      </view>

      <view class="btn secondary" :class="{ disabled: isTesting }" @tap="testDeleteSession">
        <text class="btn-text">
          测试删除会话
        </text>
      </view>

      <view class="btn info" :class="{ disabled: isTesting }" @tap="showSessionInfo">
        <text class="btn-text">
          显示会话信息
        </text>
      </view>

      <view class="btn warning" :class="{ disabled: isTesting }" @tap="refreshSessions">
        <text class="btn-text">
          刷新会话列表
        </text>
      </view>
    </view>

    <view class="status">
      <text class="status-text">
        当前状态:
      </text>
      <text class="status-value">
        会话数: {{ sessions.length }} |
        当前会话: {{ currentSessionId || '无' }} |
        加载中: {{ isLoading ? '是' : '否' }}
      </text>
      <text v-if="error" class="error-text">
        错误: {{ error }}
      </text>
    </view>

    <view class="result">
      <text class="result-title">
        测试结果:
      </text>
      <scroll-view class="result-content" scroll-y>
        <text class="result-text">
          {{ testResult || '暂无测试结果' }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<style scoped>
.session-test-container {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 32rpx;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.btn {
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.btn:not(.disabled):active {
  transform: scale(0.98);
}

.btn.disabled {
  opacity: 0.6;
}

.btn.primary {
  background-color: #007aff;
}

.btn.secondary {
  background-color: #ff4444;
}

.btn.info {
  background-color: #17a2b8;
}

.btn.warning {
  background-color: #ffc107;
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

.status {
  background-color: #ffffff;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.status-text {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 28rpx;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ff4444;
  display: block;
}

.result {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex: 1;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: block;
}

.result-content {
  height: 400rpx;
  padding: 24rpx;
}

.result-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
}
</style>
