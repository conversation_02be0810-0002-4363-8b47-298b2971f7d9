import type { ILoginRequest, IUser } from '@/api/types/login'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import {
  getUserInfo as _getUserInfo,
  login as _login,
  logout as _logout,
  wxLogin as _wxLogin,
  getUserInfoByWxAPI,
} from '@/api/login'
import { loginAPI } from '@/service/user/auth'
import { toast } from '@/utils/toast'

export const useUserStore = defineStore(
  'user',
  () => {
    // 用户详细信息
    const userDetails = ref<IUser | null>(null)
    // 用户token
    const token = ref<string>('')

    // 是否已登录的计算属性
    const isLoggedIn = computed(() => {
      return !!token.value && userDetails.value !== null
    })

    // 是否已完善个人资料
    const hasCompletedProfile = computed(() => {
      if (!userDetails.value)
        return false
      // 检查是否填写了必要的个人信息
      return !!(
        userDetails.value.name
        && userDetails.value.phoneNumber
        && userDetails.value.email
      )
    })

    // 设置用户详细信息和token
    const setUserDetails = (user: IUser, userToken: string) => {
      userDetails.value = user
      token.value = userToken

      // 持久化到本地存储
      uni.setStorageSync('userDetails', user)
      uni.setStorageSync('token', userToken)
    }

    // 设置用户头像
    const setUserAvatar = (avatar: string) => {
      if (userDetails.value) {
        userDetails.value.profilePicture = avatar
        // 更新本地存储
        uni.setStorageSync('userDetails', userDetails.value)
      }
      console.log('设置用户头像', avatar)
    }

    // 删除用户信息
    const removeUserDetails = () => {
      userDetails.value = null
      token.value = ''
      uni.removeStorageSync('userDetails')
      uni.removeStorageSync('token')
      // 清理可能存在的旧数据
      uni.removeStorageSync('userInfo')
    }

    // 从本地存储初始化用户信息
    const initUserDetails = () => {
      const savedUserDetails = uni.getStorageSync('userDetails')
      const savedToken = uni.getStorageSync('token')

      if (savedUserDetails && savedToken) {
        userDetails.value = savedUserDetails
        token.value = savedToken
      }
      else {
        // 检查是否有旧的 userInfo 数据需要清理
        const oldUserInfo = uni.getStorageSync('userInfo')
        if (oldUserInfo) {
          uni.removeStorageSync('userInfo')
        }
      }
    }

    /**
     * 获取用户信息（保留旧方法以兼容现有代码）
     */
    const getUserInfo = async () => {
      const res = await _getUserInfo()
      const userInfo = res.data

      // 将旧格式转换为新格式
      const userDetailsData: IUser = {
        id: userInfo.id,
        name: userInfo.username,
        email: '',
        role: 'user',
        userType: 'individual',
        fullName: null,
        phoneNumber: '',
        address: null,
        gender: null,
        birthDate: null,
        isActive: true,
        profilePicture: userInfo.avatar,
        profileBackgroundPicture: null,
        membershipType: 'free',
        membershipExpireDate: null,
        hasTeamPermission: false,
        freeAssessmentCount: 0,
        freeAIInterpretationCount: 0,
        totalAssessmentCount: 0,
        totalAIInterpretationCount: 0,
        archetypeData: null,
        assessmentHistory: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        loginCount: 0,
        paymentHistory: null,
        birthDateLocked: false,
      }

      setUserDetails(userDetailsData, userInfo.token)
      return res
    }

    /**
     * 用户登录
     * @param credentials 登录参数
     * @returns R<IUserLogin>
     */
    const login = async (credentials: {
      username: string
      password: string
      code: string
      uuid: string
    }) => {
      const res = await _login(credentials)
      console.log('登录信息', res)
      toast.success('登录成功')
      await getUserInfo()
      return res
    }

    /**
     * 新的用户登录方法
     * @param loginData 登录参数
     * @returns 登录响应
     */
    const userLogin = async (loginData: ILoginRequest) => {
      try {
        const res = await loginAPI(loginData)
        console.log('登录信息', res)

        if (res.data) {
          // 保存用户信息和token
          setUserDetails(res.data.user, res.data.token)
          toast.success(res.data.message || '登录成功')
        }

        return res
      }
      catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    }

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      _logout()
      removeUserDetails()
    }

    /**
     * 获取用户openid
     */
    const getUserOpenID = async (code: string) => {
      return new Promise<any>((resolve, reject) => {
        uni.request({
          url: 'https://api.weixin.qq.com/sns/jscode2session',
          data: {
            appid: import.meta.env.VITE_WX_APPID,
            secret: import.meta.env.VITE_WX_APPSECRET,
            js_code: code,
            grant_type: 'authorization_code',
          },
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          },
        })
      })
    }
    /**
     * 微信登录
     */
    const wxLogin = async () => {
      return new Promise<void>((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: async (loginRes) => {
            try {
              const userData = { code: loginRes.code }
              const res = await getUserInfoByWxAPI(userData)
              if (res.data) {
                setUserDetails(res.data.user, res.data.token)
                toast.success(res.message || '微信登录成功')
              }
              resolve()
            }
            catch (error) {
              console.error('微信登录失败:', error)
              reject(error)
            }
          },
          fail: (err) => {
            console.error('uni.login 获取 code 失败', err)
            reject(err)
          },
        })
      })
    }

    return {
      userDetails,
      token,
      isLoggedIn,
      hasCompletedProfile,
      login,
      userLogin,
      wxLogin,
      getUserInfo,
      setUserAvatar,
      setUserDetails,
      initUserDetails,
      logout,
      getUserOpenID,
    }
  },
  {
    persist: true,
  },
)
