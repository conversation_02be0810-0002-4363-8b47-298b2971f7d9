/* 心理健康APP首页样式 */

/* 顶部导航栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0 24rpx 0;
  margin-bottom: 20rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
}

.profile-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333333;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 40rpx;
}

.greeting-text {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.date-text {
  font-size: 28rpx;
  color: #999999;
}

/* 心理测评区域 */
.assessment-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 32rpx;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.assessment-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.assessment-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 基础测评卡片样式 */
.basic-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 6rpx solid #007bff;
}

/* 今日提醒卡片样式 */
.daily-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e2f0fb 100%);
  border-left: 6rpx solid #20c997;
}

/* 当下状态卡片样式 */
.current-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #f8edff 100%);
  border-left: 6rpx solid #6f42c1;
}

.card-icon {
  font-size: 60rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.card-description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  text-align: center;
}

/* 快速导入功能区域 */
.quick-action-section {
  text-align: center;
  padding: 40rpx 0;
}

.illustration {
  margin-bottom: 40rpx;
}

.action-icon {
  font-size: 120rpx;
  color: #007aff;
}

.action-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.action-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
  line-height: 1.4;
}

.start-button {
  background-color: #6f42c1;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  padding: 32rpx 80rpx;
  border-radius: 60rpx;
  display: inline-block;
  transition: all 0.3s ease;
}

.start-button:active {
  transform: scale(0.96);
  background-color: #5a32a3;
}

/* 全局样式调整 */
view {
  box-sizing: border-box;
}

/* 响应式调整 */
@media (max-width: 320px) {
  .app-title {
    font-size: 44rpx;
  }

  .card-title {
    font-size: 32rpx;
  }

  .action-title {
    font-size: 32rpx;
  }
}
