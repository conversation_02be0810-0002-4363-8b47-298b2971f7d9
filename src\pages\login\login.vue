<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录',
  },
}
</route>

<script lang="ts" setup>
import { getUserInfoByWxAPI } from '@/api/login'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

defineOptions({
  name: 'Login',
})

// 用户store
const userStore = useUserStore()

// 表单数据
const formData = reactive({
  phoneNumber: '',
  password: '',
})

// 表单验证规则
const rules = {
  phoneNumber: [
    { required: true, message: '请输入手机号' },
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度不能少于6位' },
  ],
}

// 控制密码显示/隐藏
const showPassword = ref(false)

// 登录loading状态
const loginLoading = ref(false)

// 切换密码显示状态
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

async function handleWechatLogin() {
  loginLoading.value = true
  await userStore.wxLogin()
  loginLoading.value = false
  uni.reLaunch({
    url: '/pages/index/index',
  })
}

// 表单验证
function validateForm() {
  if (!formData.phoneNumber) {
    toast.error('请输入手机号')
    return false
  }

  // if (!/^1[3-9]\d{9}$/.test(formData.phoneNumber)) {
  //   toast.error('请输入正确的手机号')
  //   return false
  // }

  if (!formData.password) {
    toast.error('请输入密码')
    return false
  }

  if (formData.password.length < 6) {
    toast.error('密码长度不能少于6位')
    return false
  }

  return true
}

// 登录处理
async function handleLogin() {
  if (!validateForm())
    return

  loginLoading.value = true

  try {
    const res = await userStore.userLogin({
      phoneNumber: formData.phoneNumber,
      password: formData.password,
    })

    if (res.data) {
      // 登录成功，跳转到首页
      uni.reLaunch({
        url: '/pages/index/index',
      })
    }
  }
  catch (error: any) {
    console.error('登录失败:', error)

    // 处理错误信息
    if (error.data && error.data.message) {
      toast.error(error.data.message)
    }
    else {
      toast.error('登录失败，请重试')
    }
  }
  finally {
    loginLoading.value = false
  }
}

function goBack() {
  uni.navigateBack()
}

function goRegister() {
  uni.navigateTo({
    url: '/pages/register/register',
  })
}

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo
// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif
</script>

<template>
  <wd-overlay :show="loginLoading">
    <view class="h-full flex items-center justify-center">
      <wd-loading type="outline" />
    </view>
  </wd-overlay>
  <view
    class="h-full" :style="{
      marginTop: `${safeAreaInsets?.top}px`,
    }"
  >
    <view class="i-material-symbols:arrow-back-ios-rounded" style="margin-left: 20rpx;" @click="goBack" />
  </view>
  <view
    class="main-content" :style="{
      height: `calc(100% - ${safeAreaInsets?.top}px)`,
    }"
  >
    <!-- 浮动粒子效果 -->
    <view class="floating-particles">
      <view class="particle" />
      <view class="particle" />
      <view class="particle" />
      <view class="particle" />
    </view>

    <view class="login-container">
      <!-- 应用头部 -->
      <view class="login-header">
        <!-- <view class="logo">
          <text class="logo-icon">
            <img src="@/static/logo.png" alt="logo">
          </text>
        </view> -->
        <h1 class="app-name" style="color: #000;">
          心智AI
        </h1>
        <p class="app-tagline" style="color: #000;">
          探索内在，成就更好的自己
        </p>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <h2 class="form-title">
          欢迎回来
        </h2>

        <view class="form-group">
          <label class="form-label">手机号</label>
          <input v-model="formData.phoneNumber" type="text" class="form-input" placeholder="请输入手机号" :maxlength="11">
        </view>

        <view class="form-group">
          <label class="form-label">密码</label>
          <view class="password-input-container">
            <input v-model="formData.password" :password="!showPassword" class="form-input" placeholder="请输入密码">
            <!-- 密码显示隐藏图标 -->
            <view
              class="password-toggle"
              :class="showPassword ? 'i-material-symbols:visibility-off' : 'i-material-symbols:visibility'"
              @click="togglePasswordVisibility"
            />
          </view>
        </view>

        <view class="w-full flex justify-between">
          <view class="form-options">
            <a href="#" class="forgot-password">忘记密码？</a>
          </view>
          <view class="form-options">
            <a href="#" class="forgot-password" @click="goRegister">立即注册</a>
          </view>
        </view>
        <button class="login-button" :disabled="loginLoading" @click="handleLogin">
          {{ loginLoading ? '登录中...' : '登录' }}
        </button>

        <!-- <view class="viewider">
          <text class="viewider-text">
            或
          </text>
        </view> -->

        <!-- #ifndef MP-WEIXIN -->
        <view class="social-login">
          <button class="social-button">
            <text class="material-symbols-outlined wechat">
              chat
            </text>
            微信
          </button>
          <button class="social-button">
            <text class="material-symbols-outlined apple">
              phone_iphone
            </text>
            Apple
          </button>
          <button class="social-button">
            <text class="material-symbols-outlined qq">
              forum
            </text>
            QQ
          </button>
        </view>
        <!-- #endif -->
        <!-- 微信显示一键登录 -->
        <!-- #ifdef MP-WEIXIN -->
        <view class="social-login">
          <button class="social-button" @click="handleWechatLogin">
            <text class="material-symbols-outlined wechat">
              微信一键登录
            </text>
          </button>
        </view>
        <!-- #endif -->
      </view>

      <!-- 快速体验 -->
      <!-- <view class="quick-login">
        <h3 class="quick-login-title">
          快速体验
        </h3>
        <view class="quick-login-buttons">
          <button class="quick-button">
            游客模式
          </button>
          <button class="quick-button">
            免费测评
          </button>
        </view>
      </view> -->

      <!-- 注册链接 -->
      <!-- <view class="signup-link">
        <text class="signup-text">
          还没有账号？
        </text>
        <a href="register.html" class="signup-button">立即注册</a>
      </view> -->
    </view>
  </view>
</template>

<style lang="scss">
@import './login.css';
//
</style>
