import { handleChatApiResponse } from '@/common/interceptors/response.interceptor'
import { httpDelete, httpGet, httpPost, httpPut } from '@/utils/http'

// 获取用户JWT token用于聊天API的Authorization header
function getAuthHeader() {
  // 直接从本地存储获取token，避免在静态方法中使用store
  const token = uni.getStorageSync('token')

  if (!token) {
    throw new Error('用户未登录，请先登录')
  }

  return {
    Authorization: `Bearer ${token}`,
  }
}

// 聊天会话接口
export interface ChatSession {
  id: string
  title: string
  description?: string
  userId: string
  status?: 'active' | 'archived' | 'deleted'
  messageCount?: number
  lastMessageAt?: string
  lastActiveAt?: string
  createdAt: string
  updatedAt: string
  metadata?: {
    topic?: string
    difficulty?: string
    [key: string]: any
  }
  messages?: ChatMessage[]
}

// 聊天消息接口
export interface ChatMessage {
  id: string
  conversationId: string
  cozeMessageId?: string | null
  role: 'user' | 'assistant'
  type: 'text' | 'image' | 'file' | 'object_string' | 'answer' | 'function_call' | 'tool_output'
  content: string
  status: 'pending' | 'completed' | 'failed'
  metadata?: {
    topic?: string
    difficulty?: string
    cozeMessageId?: string
    [key: string]: any
  }
  usage?: {
    promptTokens?: number
    completionTokens?: number
    totalTokens?: number
  } | null
  createdAt: string
  completedAt?: string | null
}

// 创建对话请求
export interface CreateConversationRequest {
  title: string
  initialMessage?: string
  region?: string
  metadata?: {
    topic?: string
    difficulty?: string
    [key: string]: any
  }
}

// 更新对话请求
export interface UpdateConversationRequest {
  title?: string
  status?: 'active' | 'archived' | 'deleted'
  metadata?: {
    [key: string]: any
  }
}

// 发送消息请求
export interface SendMessageRequest {
  conversationId: string
  content: string
  type?: 'text' | 'image' | 'file' | 'object_string'
  streaming?: boolean
  metadata?: {
    topic?: string
    difficulty?: string
    [key: string]: any
  }
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[]
  pagination: {
    total: number
    page: number
    pageSize: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 发送消息响应接口
export interface SendMessageResponse {
  userMessage: ChatMessage
  assistantMessage: ChatMessage
}

/**
 * 聊天API服务类
 */
export class ChatAPI {
  /**
   * 创建新对话
   */
  static async createConversation(data: CreateConversationRequest): Promise<ChatSession> {
    const response = await httpPost<any>('/coze/conversations', data, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 获取用户对话列表
   */
  static async getUserConversations(page: number = 1, pageSize: number = 20): Promise<PaginationResponse<ChatSession>> {
    const response = await httpGet<any>('/coze/conversations', { page, pageSize }, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 获取指定对话详情
   */
  static async getConversation(conversationId: string): Promise<ChatSession> {
    const response = await httpGet<any>(`/coze/conversations/${conversationId}`, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 更新对话信息
   */
  static async updateConversation(conversationId: string, data: UpdateConversationRequest): Promise<ChatSession> {
    const response = await httpPut<any>(`/coze/conversations/${conversationId}`, data, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 删除对话
   */
  static async deleteConversation(conversationId: string): Promise<{ success: boolean }> {
    const response = await httpDelete<any>(`/coze/conversations/${conversationId}`, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 发送AI消息(标准模式)
   */
  static async sendMessage(data: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await httpPost<any>('/coze/messages', data, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 发送AI消息(流式模式)
   * 使用微信小程序官方的流式传输实现
   */
  static async sendMessageStream(
    data: SendMessageRequest,
    callbacks: {
      onMessage?: (event: string, data: any) => void
      onError?: (error: any) => void
      onComplete?: () => void
    },
  ): Promise<{ requestTask: any, promise: Promise<void> }> {
    const streamData = { ...data, streaming: true }

    let accumulatedText = ''
    let requestTask: any

    const promise = new Promise<void>((resolve, reject) => {
      const requestOptions: any = {
        url: `${import.meta.env.VITE_SERVER_BASEURL || ''}/coze/messages/stream`,
        method: 'POST',
        data: streamData,
        responseType: 'arraybuffer', // 关键！接收二进制数据
        enableChunked: true, // 关键！开启流式传输模式
        header: {
          'Content-Type': 'application/json',
          ...getAuthHeader(),
        },
        timeout: 300000, // 5分钟超时
        success: (res) => {
          console.log('流式请求完成，状态码:', res.statusCode)
          callbacks.onComplete?.()
          resolve()
        },
        fail: (err) => {
          console.error('流式请求失败:', err)
          callbacks.onError?.(err)
          reject(err)
        },
      }

      // 发起请求
      requestTask = uni.request(requestOptions)

      // 监听数据分块接收事件
      if (requestTask.onChunkReceived) {
        requestTask.onChunkReceived((response: any) => {
          try {
            console.log('收到分块数据:', response)

            // 使用官方推荐的方式处理 ArrayBuffer
            const data16 = ChatAPI.buf2hex(response.data)
            const responseText = ChatAPI.hexToStr(data16)

            // 累积文本数据
            accumulatedText += responseText

            // 处理累积的数据，查找完整的事件
            ChatAPI.processStreamChunks(accumulatedText, callbacks, (processedLength) => {
              // 移除已处理的数据
              accumulatedText = accumulatedText.substring(processedLength)
            })
          }
          catch (error) {
            console.error('处理分块数据失败:', error)
            callbacks.onError?.(error)
          }
        })

        // 监听请求头接收事件
        requestTask.onHeadersReceived?.((res: any) => {
          console.log('收到响应头:', res.headers)
          console.log('响应状态码:', res.statusCode)
        })
      }
      else {
        console.warn('当前环境不支持 onChunkReceived，可能需要更新微信开发工具或基础库版本')
        callbacks.onError?.(new Error('当前环境不支持流式传输'))
        reject(new Error('当前环境不支持流式传输'))
      }
    })

    return {
      requestTask,
      promise,
    }
  }

  /**
   * 将 ArrayBuffer 转换为16进制字符串
   * 官方推荐的处理方式
   */
  private static buf2hex(buffer: ArrayBuffer): string {
    const hexArr = Array.prototype.map.call(
      new Uint8Array(buffer),
      (bit: number) => (`00${bit.toString(16)}`).slice(-2),
    )
    return hexArr.join('')
  }

  /**
   * 将16进制字符串转换为UTF-8字符串
   * 官方推荐的处理方式
   */
  private static hexToStr(hex: string): string {
    let str = ''
    for (let i = 0; i < hex.length; i += 2) {
      const hexByte = hex.substr(i, 2)
      const charCode = Number.parseInt(hexByte, 16)
      str += String.fromCharCode(charCode)
    }

    try {
      // 尝试解码UTF-8
      return decodeURIComponent(escape(str))
    }
    catch (e) {
      // 如果解码失败，直接返回原字符串
      console.warn('UTF-8解码失败，返回原字符串:', e)
      return str
    }
  }

  /**
   * 将 ArrayBuffer 转换为字符串（备用方法）
   */
  private static arrayBufferToString(buffer: ArrayBuffer): string {
    try {
      // 使用新的官方推荐方式
      const data16 = ChatAPI.buf2hex(buffer)
      return ChatAPI.hexToStr(data16)
    }
    catch (error) {
      console.error('ArrayBuffer 转换失败:', error)
      // 最后的备用方案
      return String(buffer || '')
    }
  }

  /**
   * 处理流式数据块
   */
  private static processStreamChunks(
    accumulatedData: string,
    callbacks: {
      onMessage?: (event: string, data: any) => void
      onError?: (error: any) => void
      onComplete?: () => void
    },
    onProcessed: (processedLength: number) => void,
  ) {
    let processedLength = 0
    const lines = accumulatedData.split('\n')

    for (let i = 0; i < lines.length - 1; i++) { // 最后一行可能不完整，跳过
      const line = lines[i].trim()

      if (!line) {
        processedLength += lines[i].length + 1 // +1 for \n
        continue
      }

      // 处理 "data: " 前缀
      if (line.startsWith('data: ')) {
        const jsonData = line.substring(6).trim()

        if (jsonData === '') {
          processedLength += lines[i].length + 1
          continue
        }

        try {
          const parsedData = JSON.parse(jsonData)
          console.log('解析流式事件:', parsedData)

          // 立即发送事件
          callbacks.onMessage?.(parsedData.event, parsedData.data)

          // 检查是否完成
          if (parsedData.event === 'done') {
            console.log('流式传输完成')
            callbacks.onComplete?.()
          }

          processedLength += lines[i].length + 1
        }
        catch (parseError) {
          console.error('解析流式数据失败:', parseError, '原始数据:', jsonData)
          processedLength += lines[i].length + 1
        }
      }
      else {
        processedLength += lines[i].length + 1
      }
    }

    // 通知已处理的长度
    if (processedLength > 0) {
      onProcessed(processedLength)
    }
  }

  /**
   * 获取对话的消息历史
   */
  static async getConversationMessages(conversationId: string, page: number = 1, pageSize: number = 50): Promise<PaginationResponse<ChatMessage>> {
    const response = await httpGet<any>(`/coze/conversations/${conversationId}/messages`, { page, pageSize }, getAuthHeader())
    return handleChatApiResponse(response)
  }

  /**
   * 取消正在进行的AI对话
   */
  static async cancelChat(conversationId: string, chatId: string): Promise<{ success: boolean }> {
    const response = await httpDelete<any>(`/coze/conversations/${conversationId}/chats/${chatId}`, {}, getAuthHeader())
    return handleChatApiResponse(response)
  }
}
