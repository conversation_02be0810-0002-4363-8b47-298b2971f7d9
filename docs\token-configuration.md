# Token配置和API接口说明

## 概述

本项目已从使用硬编码的Coze token改为使用后端API接口，通过JWT token进行用户认证。

## 认证方式

### 前端认证
- 使用JWT token进行用户认证
- JWT token存储在本地存储中（`uni.getStorageSync('token')`）
- 所有API请求都会自动在请求头中包含JWT token

### API请求头
- **X-JWT-Token**: 包含用户的JWT token（用于后端验证用户身份）
- **Authorization**: 包含JWT token（用于AI聊天接口）

## API接口

### 聊天相关接口

#### 1. 创建对话
- **接口**: `POST /coze/conversations`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`
- **请求体**:
```json
{
  "title": "对话标题",
  "region": "zh",
  "metadata": {
    "topic": "mental_health",
    "difficulty": "beginner"
  }
}
```

#### 2. 获取用户对话列表
- **接口**: `GET /coze/conversations?page=1&pageSize=20`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`
- **响应格式**:
```json
{
  "data": [
    {
      "id": "会话ID",
      "title": "会话标题",
      "createdAt": "创建时间",
      "messages": [...]
    }
  ],
  "pagination": {
    "total": 总数,
    "page": 当前页,
    "pageSize": 每页大小,
    "totalPages": 总页数
  }
}
```

#### 3. 发送消息
- **接口**: `POST /coze/messages`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`
- **请求体**:
```json
{
  "conversationId": "会话ID",
  "content": "消息内容",
  "type": "text",
  "streaming": false,
  "metadata": {
    "topic": "mental_health"
  }
}
```
- **响应格式**:
```json
{
  "userMessage": {
    "id": "用户消息ID",
    "content": "用户消息内容",
    "role": "user",
    "createdAt": "创建时间"
  },
  "assistantMessage": {
    "id": "AI消息ID",
    "content": "AI回复内容",
    "role": "assistant",
    "createdAt": "创建时间"
  }
}
```

#### 4. 删除对话
- **接口**: `DELETE /coze/conversations/{conversationId}`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`
- **响应格式**:
```json
{
  "success": true
}
```

#### 5. 更新对话
- **接口**: `PUT /coze/conversations/{conversationId}`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`
- **请求体**:
```json
{
  "title": "新标题",
  "status": "active"
}
```

#### 6. 获取对话详情
- **接口**: `GET /coze/conversations/{conversationId}`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`

#### 7. 获取对话消息历史
- **接口**: `GET /coze/conversations/{conversationId}/messages?page=1&pageSize=50`
- **请求头**: `Authorization: Bearer {JWT_TOKEN}`

## 会话管理功能

### 功能特性
1. **会话列表**: 显示用户的所有对话会话
2. **切换会话**: 点击会话可以切换到对应的对话
3. **新建会话**: 创建新的对话会话
4. **删除会话**: 删除不需要的对话会话
5. **刷新会话**: 从服务器刷新会话列表

### 界面操作
- **会话列表按钮**: 点击顶部导航栏的📋图标打开会话列表
- **新建会话按钮**: 点击顶部导航栏的➕图标创建新会话
- **会话操作**: 长按会话项或点击⋯按钮显示操作菜单
- **删除确认**: 点击删除按钮会弹出确认菜单

### 会话信息显示
- **会话标题**: 显示对话的主题或标题
- **消息预览**: 显示最后一条消息的预览内容
- **时间显示**: 显示会话的创建时间（今天显示时间，昨天显示"昨天"，其他显示日期）
- **当前状态**: 高亮显示当前选中的会话

## 响应拦截器

项目使用全局响应拦截器 `src/common/interceptors/response.interceptor.ts` 来统一处理后端API响应格式。

### 响应格式
所有API响应都会通过拦截器处理，确保返回统一的数据格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体的数据内容
  }
}
```

### 错误处理
- 自动处理网络错误
- 统一错误消息格式
- 自动显示错误提示

## 测试页面

### 聊天功能测试
- 页面路径: `/pages/about/chat-test`
- 功能: 测试发送消息、接收AI回复等基本聊天功能

### 会话管理测试
- 页面路径: `/pages/about/session-test`
- 功能: 测试会话的创建、切换、删除、刷新等管理功能

## 注意事项

1. **JWT Token**: 确保用户已登录并获取到有效的JWT token
2. **网络连接**: 所有API调用都需要网络连接
3. **错误处理**: 网络错误或API错误会自动显示提示信息
4. **会话状态**: 会话状态会自动同步到本地，支持离线查看历史消息
5. **消息格式**: 支持文本消息，后续可扩展支持图片、文件等类型

## 开发调试

### 查看网络请求
在浏览器开发者工具的Network面板中查看API请求和响应

### 查看控制台日志
- 成功请求会显示响应数据
- 错误请求会显示详细的错误信息
- 会话管理操作会显示操作结果

### 测试建议
1. 先使用测试页面验证API连接正常
2. 测试基本的发送消息功能
3. 测试会话管理功能
4. 测试错误情况下的处理 