import type { ChatMessage, ChatSession, CreateConversationRequest, SendMessageRequest, SendMessageResponse } from '@/api/chat'
import { computed, ref } from 'vue'
import { ChatAPI } from '@/api/chat'

// 本地消息格式
export interface LocalMessage {
  id: string
  content: string
  type: 'user' | 'ai'
  timestamp: number
  isLoading?: boolean
  isStreaming?: boolean // 标识是否正在流式传输
  fullContent?: string // 完整内容（用于流式传输）
  displayContent?: string // 当前显示的内容（用于逐字显示）
  typingTimer?: number // 逐字显示的定时器ID
}

// 本地会话格式
export interface LocalChatSession {
  id: string
  title: string
  messages: LocalMessage[]
  createTime: number
  lastActiveAt?: number
  messageCount?: number
  status?: string
}

export function useRealChat() {
  // 响应式状态
  const sessions = ref<LocalChatSession[]>([])
  const currentSessionId = ref<string>('')
  const isLoading = ref(false)
  const isAITyping = ref(false)
  const error = ref<string>('')

  // 流式传输相关状态
  const isStreamingEnabled = ref(true)
  const currentStreamingMessageId = ref<string>('')
  const currentStreamingRequest = ref<any>(null) // 存储当前流式请求的引用
  const streamingChunkCount = ref(0) // 接收到的数据块计数

  // 计算属性
  const currentSession = computed(() =>
    sessions.value.find(s => s.id === currentSessionId.value),
  )

  const messageList = computed(() => currentSession.value?.messages || [])

  // 辅助函数：生成唯一ID
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 辅助函数：将API消息转换为本地消息格式
  function convertApiMessageToLocal(apiMessage: ChatMessage): LocalMessage {
    return {
      id: apiMessage.id,
      content: apiMessage.content,
      type: apiMessage.role === 'user' ? 'user' : 'ai',
      timestamp: new Date(apiMessage.createdAt).getTime(),
    }
  }

  // 辅助函数：将API会话转换为本地会话格式
  function convertApiSessionToLocal(apiSession: ChatSession): LocalChatSession {
    // 将消息按照创建时间排序，确保最新的消息在最后
    const sortedMessages = (apiSession.messages || [])
      .map(convertApiMessageToLocal)
      .sort((a, b) => a.timestamp - b.timestamp)

    return {
      id: apiSession.id,
      title: apiSession.title,
      messages: sortedMessages,
      createTime: new Date(apiSession.createdAt).getTime(),
      lastActiveAt: apiSession.lastActiveAt ? new Date(apiSession.lastActiveAt).getTime() : new Date(apiSession.createdAt).getTime(),
      messageCount: apiSession.messageCount || 0,
      status: apiSession.status || 'active',
    }
  }

  // 错误处理
  function handleError(err: any, defaultMessage: string) {
    console.error(defaultMessage, err)
    console.error('错误详情:', {
      message: err?.message,
      data: err?.data,
      status: err?.status,
      statusCode: err?.statusCode,
      errMsg: err?.errMsg,
      type: typeof err,
    })

    // 优先使用 Error 对象的 message 属性
    if (err?.message) {
      error.value = err.message
    }
    // 然后尝试使用 uni.request 的 errMsg 属性
    else if (err?.errMsg) {
      if (err.errMsg.includes('timeout')) {
        error.value = '请求超时，请检查网络连接或稍后重试'
      }
      else if (err.errMsg.includes('fail')) {
        error.value = '网络连接失败，请检查网络设置'
      }
      else {
        error.value = `网络错误: ${err.errMsg}`
      }
    }
    // 尝试使用 API 响应的错误消息
    else if (err?.data?.message) {
      error.value = err.data.message
    }
    // 处理字符串类型的错误
    else if (typeof err === 'string') {
      error.value = err
    }
    // 使用默认错误消息
    else {
      error.value = defaultMessage
    }
  }

  // 清除错误
  function clearError() {
    error.value = ''
  }

  // 切换流式传输模式
  function toggleStreamingMode() {
    isStreamingEnabled.value = !isStreamingEnabled.value
    uni.showToast({
      title: isStreamingEnabled.value ? '已启用流式传输' : '已禁用流式传输',
      icon: 'success',
      duration: 1500,
    })
  }

  // 显示AI正在输入状态
  function showAITyping() {
    isAITyping.value = true

    const currentSess = currentSession.value
    if (!currentSess)
      return

    const loadingMessage: LocalMessage = {
      id: `loading-${generateId()}`,
      content: '',
      type: 'ai',
      timestamp: Date.now(),
      isLoading: true,
    }

    currentSess.messages.push(loadingMessage)
    // 强制触发响应式更新
    currentSess.messages = [...currentSess.messages]
  }

  // 隐藏AI正在输入状态
  function hideAITyping() {
    isAITyping.value = false

    const currentSess = currentSession.value
    if (!currentSess)
      return

    // 移除加载消息
    currentSess.messages = currentSess.messages.filter(msg => !msg.isLoading)
    // 强制触发响应式更新
    currentSess.messages = [...currentSess.messages]
  }

  // 为流式传输创建空的AI消息
  function createStreamingMessage(): LocalMessage {
    return {
      id: generateId(),
      content: '',
      type: 'ai',
      timestamp: Date.now(),
      isStreaming: true,
      fullContent: '',
      displayContent: '',
      typingTimer: undefined,
    }
  }

  // 更新流式传输的消息内容
  function updateStreamingMessage(messageId: string, content: string, isComplete: boolean = false) {
    const currentSess = currentSession.value
    if (!currentSess)
      return

    const messageIndex = currentSess.messages.findIndex(msg => msg.id === messageId)
    if (messageIndex !== -1) {
      const message = currentSess.messages[messageIndex]

      // 直接更新内容，流式传输本身就提供了逐步效果
      message.content = content
      message.fullContent = content
      message.displayContent = content

      if (isComplete) {
        // 如果完成，标记为非流式状态
        message.isStreaming = false
      }

      // 强制触发响应式更新
      currentSess.messages = [...currentSess.messages]
    }
  }

  // 清理流式传输定时器
  function clearStreamingTimers() {
    sessions.value.forEach((session) => {
      session.messages.forEach((message) => {
        if (message.typingTimer) {
          clearInterval(message.typingTimer)
          message.typingTimer = undefined
        }
      })
    })
  }

  // 清理特定消息的定时器
  function clearMessageTimer(messageId: string) {
    const currentSess = currentSession.value
    if (!currentSess)
      return

    const message = currentSess.messages.find(msg => msg.id === messageId)
    if (message && message.typingTimer) {
      clearInterval(message.typingTimer)
      message.typingTimer = undefined
    }
  }

  // 逐字显示动画
  function startTypingAnimation(messageIndex: number, targetContent: string) {
    const currentSess = currentSession.value
    if (!currentSess || !currentSess.messages[messageIndex])
      return

    const message = currentSess.messages[messageIndex]
    const currentDisplayContent = message.displayContent || ''

    // 如果目标内容没有变化，不需要更新
    if (currentDisplayContent === targetContent)
      return

    // 清除之前的定时器
    if (message.typingTimer) {
      clearInterval(message.typingTimer)
    }

    // 设置逐字显示的速度（毫秒）
    const typingSpeed = 20 // 每20毫秒显示一个字符，更快的打字效果
    let currentIndex = currentDisplayContent.length

    message.typingTimer = setInterval(() => {
      if (currentIndex < targetContent.length) {
        // 逐字更新显示内容
        message.displayContent = targetContent.substring(0, currentIndex + 1)
        message.content = message.displayContent
        currentIndex++

        // 强制触发响应式更新
        currentSess.messages = [...currentSess.messages]
      }
      else {
        // 显示完成，清除定时器
        clearInterval(message.typingTimer!)
        message.typingTimer = undefined
        message.displayContent = targetContent
        message.content = targetContent

        // 强制触发响应式更新
        currentSess.messages = [...currentSess.messages]
      }
    }, typingSpeed)
  }

  // 发送消息（支持流式传输）
  async function sendMessage(content: string) {
    if (!content.trim() || isAITyping.value || !currentSessionId.value) {
      return
    }

    const currentSess = currentSession.value
    if (!currentSess) {
      error.value = '未找到当前会话'
      return
    }

    // 添加用户消息到本地状态
    const userMessage: LocalMessage = {
      id: generateId(),
      content: content.trim(),
      type: 'user',
      timestamp: Date.now(),
    }

    currentSess.messages.push(userMessage)
    // 强制触发响应式更新
    currentSess.messages = [...currentSess.messages]

    // 显示AI正在输入状态
    showAITyping()

    try {
      // 构造发送消息的请求
      const messageData: SendMessageRequest = {
        conversationId: currentSessionId.value,
        content: content.trim(),
        type: 'text',
        streaming: isStreamingEnabled.value,
        metadata: {
          topic: 'mental_health',
        },
      }

      if (isStreamingEnabled.value) {
        // 使用微信小程序官方流式传输
        await sendMessageWithWeChatStreaming(messageData)
      }
      else {
        // 使用标准模式
        await sendMessageWithStandard(messageData, currentSess)
      }
    }
    catch (err: any) {
      handleError(err, '发送消息失败')
      hideAITyping()

      // 清理流式传输相关状态
      if (currentStreamingRequest.value) {
        currentStreamingRequest.value = null
      }
      clearMessageTimer(currentStreamingMessageId.value)
      currentStreamingMessageId.value = ''

      // 添加错误提示消息
      const errorMessage: LocalMessage = {
        id: generateId(),
        content: '抱歉，消息发送失败，请稍后重试。',
        type: 'ai',
        timestamp: Date.now(),
      }
      currentSess.messages.push(errorMessage)

      uni.showToast({
        title: '发送失败',
        icon: 'error',
        duration: 2000,
      })
    }
  }

  // 标准模式发送消息
  async function sendMessageWithStandard(messageData: SendMessageRequest, currentSess: LocalChatSession) {
    const response: SendMessageResponse = await ChatAPI.sendMessage(messageData)

    console.log('API响应数据:', response)

    // 移除加载状态
    hideAITyping()

    // 处理API响应
    if (response && response.assistantMessage) {
      // 使用返回的AI消息
      const aiMessage = convertApiMessageToLocal(response.assistantMessage)
      console.log('转换后的AI消息:', aiMessage)

      // 添加AI回复到消息列表
      currentSess.messages.push(aiMessage)
      console.log('AI消息已添加，当前消息列表长度:', currentSess.messages.length)
    }
    else {
      // 兼容旧格式或错误情况
      console.error('响应格式错误:', response)
      throw new Error('无效的API响应格式')
    }
  }

  // 微信小程序官方流式传输发送消息
  async function sendMessageWithWeChatStreaming(messageData: SendMessageRequest) {
    const currentSess = currentSession.value
    if (!currentSess)
      return

    // 移除加载状态
    hideAITyping()

    // 重置数据块计数
    streamingChunkCount.value = 0

    // 如果是重试，找到现有的流式消息，否则创建新的
    let streamingMessageId = currentStreamingMessageId.value
    if (!streamingMessageId) {
      const streamingMessage = createStreamingMessage()
      streamingMessageId = streamingMessage.id
      currentStreamingMessageId.value = streamingMessageId
      currentSess.messages.push(streamingMessage)
      currentSess.messages = [...currentSess.messages]
    }

    // 环境检测信息
    console.log('=== 微信小程序流式传输开始 ===')
    // #ifdef MP-WEIXIN
    const systemInfo = uni.getSystemInfoSync()
    console.log(`运行环境: 微信小程序`)
    console.log(`基础库版本: ${systemInfo.SDKVersion}`)
    console.log(`微信版本: ${systemInfo.version}`)

    // 检查流式传输支持
    if (typeof uni.canIUse === 'function') {
      const supportsChunked = uni.canIUse('request.enableChunked')
      const supportsOnChunkReceived = uni.canIUse('requestTask.onChunkReceived')
      console.log(`enableChunked 支持: ${supportsChunked ? '✅' : '❌'}`)
      console.log(`onChunkReceived 支持: ${supportsOnChunkReceived ? '✅' : '❌'}`)
    }
    // #endif

    // #ifdef H5
    console.log('⚠️ 当前运行在H5环境，可能不支持流式传输')
    // #endif

    try {
      // 发送流式传输请求
      const result = await ChatAPI.sendMessageStream(messageData, {
        onMessage: (event: string, data: any) => {
          streamingChunkCount.value++
          console.log(`📦 收到流式事件 #${streamingChunkCount.value}: ${event}`, data)
          handleWeChatStreamingEvent(event, data)
        },
        onError: (error: any) => {
          console.error('❌ 微信流式传输错误:', error)
          handleStreamingError(error, messageData)
        },
        onComplete: () => {
          console.log(`🎉 微信流式传输完成，总计收到 ${streamingChunkCount.value} 个数据块`)
          // 清理当前消息的定时器并标记完成
          clearMessageTimer(currentStreamingMessageId.value)
          // 清理请求引用
          currentStreamingRequest.value = null
          // 只清除当前流式消息ID
          currentStreamingMessageId.value = ''
        },
      })

      // 存储请求引用，以便取消
      currentStreamingRequest.value = result.requestTask

      // 等待请求完成
      await result.promise
    }
    catch (error) {
      console.error('💥 微信流式传输请求失败:', error)
      handleStreamingError(error, messageData)
    }
  }

  // 取消流式传输
  function cancelStreamingMessage() {
    if (currentStreamingRequest.value) {
      // 取消请求
      if (typeof currentStreamingRequest.value.abort === 'function') {
        currentStreamingRequest.value.abort()
      }
      currentStreamingRequest.value = null

      // 清理当前消息的定时器
      clearMessageTimer(currentStreamingMessageId.value)

      // 更新消息为取消状态
      updateStreamingMessage(
        currentStreamingMessageId.value,
        '已取消发送',
        true,
      )

      // 清除流式消息ID
      currentStreamingMessageId.value = ''

      console.log('🚫 用户取消了流式传输')
      uni.showToast({
        title: '已取消发送',
        icon: 'success',
        duration: 1500,
      })
    }
  }

  // 处理流式传输错误
  function handleStreamingError(error: any, messageData: SendMessageRequest) {
    // 直接处理错误，不进行自动重试
    handleError(error, '微信流式传输失败')

    // 清理当前消息的定时器
    clearMessageTimer(currentStreamingMessageId.value)

    // 清理请求引用
    currentStreamingRequest.value = null

    // 更新消息为错误状态
    let errorMessage = '抱歉，消息发送失败，请稍后重试。'
    if (error.message?.includes('超时')) {
      errorMessage = '请求超时，请检查网络连接后重试。'
    }
    else if (error.message?.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络设置后重试。'
    }
    else if (error.message?.includes('不支持流式传输')) {
      errorMessage = '当前环境不支持流式传输，请更新微信版本或基础库。'
    }

    updateStreamingMessage(
      currentStreamingMessageId.value,
      errorMessage,
      true,
    )

    // 显示错误提示
    uni.showToast({
      title: '发送失败',
      icon: 'error',
      duration: 2000,
    })

    // 清除流式消息ID
    currentStreamingMessageId.value = ''
  }

  // 处理微信小程序流式传输事件
  function handleWeChatStreamingEvent(event: string, data: any) {
    console.log(`📋 处理微信流式事件: ${event}`, data)

    switch (event) {
      case 'user_message':
        // 用户消息事件，通常不需要处理
        console.log('👤 用户消息事件:', data)
        break

      case 'assistant_message_created':
        // AI消息创建事件
        console.log('🤖 AI消息创建:', data)
        break

      case 'delta':
        // 增量内容事件
        console.log('🔄 Delta增量事件:', data)
        if (data && (data.fullContent || data.content)) {
          const content = data.fullContent || data.content
          updateStreamingMessage(currentStreamingMessageId.value, content)
          console.log(`📝 更新内容长度: ${content.length} 字符`)
        }
        break

      case 'message':
        // 消息更新事件
        console.log('📨 消息更新事件:', data)
        if (data && data.content) {
          updateStreamingMessage(currentStreamingMessageId.value, data.content)
          console.log(`📝 消息更新长度: ${data.content.length} 字符`)
        }
        break

      case 'completed':
        // 完成事件
        console.log('✅ 消息完成事件:', data)
        if (data && (data.fullContent || data.content)) {
          const content = data.fullContent || data.content
          updateStreamingMessage(currentStreamingMessageId.value, content, true)
          console.log(`📝 最终内容长度: ${content.length} 字符`)
        }
        break

      case 'done': {
        // 传输完成事件 - 不更新内容，只标记完成
        console.log('🏁 传输完成事件')
        // 找到当前流式消息并标记为完成
        const currentSess = currentSession.value
        if (currentSess && currentStreamingMessageId.value) {
          const messageIndex = currentSess.messages.findIndex(msg => msg.id === currentStreamingMessageId.value)
          if (messageIndex !== -1) {
            currentSess.messages[messageIndex].isStreaming = false
            currentSess.messages = [...currentSess.messages]
            console.log('🔄 消息标记为完成状态')
          }
        }
        break
      }

      case 'error': {
        // 错误事件
        console.error('❌ 服务端错误事件:', data)
        const errorMessage = data?.message || '服务器返回错误'
        handleStreamingError(new Error(errorMessage), {} as SendMessageRequest)
        break
      }

      default:
        console.log(`❓ 未知流式事件: ${event}`, data)
    }
  }

  // 初始化会话
  async function initializeSessions() {
    isLoading.value = true
    error.value = ''

    try {
      // 尝试获取用户的对话列表
      const response = await ChatAPI.getUserConversations(1, 20)
      console.log('获取对话列表响应:', response)

      // 修复：根据实际API返回结构访问数据
      const conversations = (response as any).conversations || response.data || []
      if (conversations && conversations.length > 0) {
        // 转换为本地格式
        sessions.value = conversations.map(convertApiSessionToLocal)
        currentSessionId.value = sessions.value[0].id
        console.log('成功加载会话列表:', sessions.value)
      }
      else {
        // 如果没有对话，创建默认会话
        console.log('没有找到对话，创建默认会话')
        await createDefaultSession()
      }
    }
    catch (err: any) {
      handleError(err, '初始化会话失败')
      console.error('初始化会话失败:', err)

      // 创建本地临时会话作为后备
      await createDefaultSession()
    }
    finally {
      isLoading.value = false
    }
  }

  // 创建默认会话
  async function createDefaultSession() {
    try {
      const defaultSessionData: CreateConversationRequest = {
        title: '新的对话',
        region: 'zh',
        metadata: {
          topic: 'mental_health',
          difficulty: 'beginner',
        },
      }

      const newSession = await ChatAPI.createConversation(defaultSessionData)
      const localSession = convertApiSessionToLocal(newSession)

      // 添加欢迎消息
      if (!localSession.messages.length) {
        localSession.messages.push({
          id: 'welcome-1',
          content: '您好！我是您的心理健康AI助手，很高兴为您服务。我可以帮助您进行心理评估、情绪分析、压力管理等。请告诉我您今天的感受或想咨询的问题。',
          type: 'ai',
          timestamp: Date.now(),
        })
      }

      sessions.value = [localSession]
      currentSessionId.value = localSession.id
    }
    catch (err: any) {
      handleError(err, '创建默认会话失败')

      // 如果API调用失败，创建本地临时会话
      const fallbackSession: LocalChatSession = {
        id: 'local-default',
        title: '新的对话',
        messages: [{
          id: 'welcome-local',
          content: '抱歉，无法连接到服务器。这是一个本地临时会话。',
          type: 'ai',
          timestamp: Date.now(),
        }],
        createTime: Date.now(),
      }

      sessions.value = [fallbackSession]
      currentSessionId.value = fallbackSession.id
    }
  }

  // 创建新会话
  async function createNewSession() {
    isLoading.value = true
    error.value = ''

    try {
      const sessionData: CreateConversationRequest = {
        title: `对话 ${sessions.value.length + 1}`,
        region: 'zh',
        metadata: {
          topic: 'general',
          difficulty: 'beginner',
        },
      }

      const newSession = await ChatAPI.createConversation(sessionData)
      const localSession = convertApiSessionToLocal(newSession)

      // 添加欢迎消息
      if (!localSession.messages.length) {
        localSession.messages.push({
          id: `welcome-${generateId()}`,
          content: '您好！我是您的心理健康AI助手，很高兴为您服务。有什么我可以帮助您的吗？',
          type: 'ai',
          timestamp: Date.now(),
        })
      }

      sessions.value.unshift(localSession)
      currentSessionId.value = localSession.id

      uni.showToast({
        title: '新会话已创建',
        icon: 'success',
        duration: 1500,
      })
    }
    catch (err: any) {
      handleError(err, '创建新会话失败')

      uni.showToast({
        title: '创建会话失败',
        icon: 'error',
        duration: 2000,
      })
    }
    finally {
      isLoading.value = false
    }
  }

  // 切换会话
  function switchSession(sessionId: string) {
    currentSessionId.value = sessionId
  }

  // 删除会话
  async function deleteSession(sessionId: string) {
    try {
      await ChatAPI.deleteConversation(sessionId)

      // 从本地状态中移除
      sessions.value = sessions.value.filter(s => s.id !== sessionId)

      // 如果删除的是当前会话，切换到其他会话
      if (currentSessionId.value === sessionId) {
        if (sessions.value.length > 0) {
          currentSessionId.value = sessions.value[0].id
        }
        else {
          // 如果没有其他会话，创建新会话
          await createNewSession()
        }
      }

      uni.showToast({
        title: '会话已删除',
        icon: 'success',
        duration: 1500,
      })
    }
    catch (err: any) {
      handleError(err, '删除会话失败')

      uni.showToast({
        title: '删除失败',
        icon: 'error',
        duration: 2000,
      })
    }
  }

  // 刷新会话列表
  async function refreshSessions() {
    try {
      const response = await ChatAPI.getUserConversations(1, 20)
      console.log('刷新会话列表响应:', response)

      // 修复：根据实际API返回结构访问数据
      const conversations = (response as any).conversations || response.data || []
      if (conversations) {
        sessions.value = conversations.map(convertApiSessionToLocal)
        console.log('刷新后的会话列表:', sessions.value)
      }

      // 如果当前会话不在列表中，切换到第一个会话
      if (!sessions.value.find(s => s.id === currentSessionId.value)) {
        if (sessions.value.length > 0) {
          currentSessionId.value = sessions.value[0].id
        }
      }
    }
    catch (err: any) {
      handleError(err, '刷新会话列表失败')
    }
  }

  return {
    // 状态
    sessions,
    currentSessionId,
    currentSession,
    messageList,
    isLoading,
    isAITyping,
    error,
    isStreamingEnabled,
    currentStreamingMessageId,
    currentStreamingRequest,
    streamingChunkCount, // 新增：暴露流式传输数据块计数

    // 方法
    initializeSessions,
    createNewSession,
    sendMessage,
    switchSession,
    deleteSession,
    refreshSessions,
    clearError,
    toggleStreamingMode, // 新增：暴露切换流式传输模式的方法
    clearStreamingTimers, // 新增：暴露清理定时器的方法
    cancelStreamingMessage, // 新增：暴露取消流式传输的方法
  }
}
