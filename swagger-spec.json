{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/api-json": {"get": {"operationId": "AppController_getSwagger<PERSON>son", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/health": {"get": {"operationId": "HealthController_checkHealth", "parameters": [], "responses": {"200": {"description": "健康检查结果", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"database": {"status": "connected", "config": {"type": "mysql", "host": "localhost", "port": 3306, "database": "kanli"}}, "redis": {"status": "connected", "config": {"host": "localhost", "port": 6379, "db": 0}}, "overall": "healthy"}}}}}}}, "summary": "检查所有服务健康状态", "tags": ["健康检查"]}}, "/health/simple": {"get": {"operationId": "HealthController_simpleHealthCheck", "parameters": [], "responses": {"200": {"description": "简单检查结果", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"status": "healthy", "timestamp": "2024-01-15T10:30:00.000Z"}}}}}}}, "summary": "简单健康检查", "tags": ["健康检查"]}}, "/health/database": {"get": {"operationId": "HealthController_checkDatabase", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "检查数据库连接", "tags": ["健康检查"]}}, "/health/redis": {"get": {"operationId": "HealthController_checkRedis", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "检查Redis连接", "tags": ["健康检查"]}}, "/captcha/send": {"post": {"operationId": "CaptchaController_sendCaptcha", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCaptchaDto"}}}}, "responses": {"200": {"description": "验证码发送成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"phoneNumber": "13800138000", "message": "验证码已发送，请注意查收", "expiresIn": 300}}}}}}, "400": {"description": "请求参数错误或发送过于频繁"}}, "summary": "发送验证码", "tags": ["验证码"]}}, "/captcha/check-rate-limit": {"get": {"operationId": "CaptchaController_checkRateLimit", "parameters": [{"name": "phoneNumber", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"isLimited": true, "remainingTime": 30}}}}}}}, "summary": "检查发送频率限制", "tags": ["验证码"]}}, "/captcha/remaining-time": {"get": {"operationId": "CaptchaController_getRemainingTime", "parameters": [{"name": "phoneNumber", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"remainingTime": 240}}}}}}}, "summary": "获取验证码剩余有效时间", "tags": ["验证码"]}}, "/users/register": {"post": {"operationId": "UsersController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "isActive": true, "membershipType": "free", "freeAssessmentCount": 10, "freeAIInterpretationCount": 10, "createdAt": "2024-01-15T10:30:00.000Z"}, "message": "注册成功"}}}}}}, "400": {"description": "注册失败，参数错误或验证码无效"}}, "summary": "注册用户", "tags": ["用户"]}}, "/users/login": {"post": {"operationId": "UsersController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginUserDto"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "membershipType": "free"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "message": "登录成功"}}}}}}, "400": {"description": "登录失败，用户不存在或密码错误"}}, "summary": "用户登录", "tags": ["用户"]}}, "/users/logout": {"post": {"operationId": "UsersController_logout", "parameters": [{"name": "X-JWT-Token", "required": true, "in": "header", "description": "JWT Bearer token", "schema": {"type": "string"}}], "responses": {"200": {"description": "退出登录成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"message": "退出登录成功", "timestamp": "2024-01-15T10:30:00.000Z"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponseDto"}}}}, "400": {"description": "请求头缺少X-JWT-Token"}, "401": {"description": "未授权，token无效或已过期"}}, "summary": "退出登录", "tags": ["用户"]}}, "/users/profile": {"get": {"operationId": "UsersController_getProfile", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"user": {"id": 1, "name": "张三", "email": "<EMAIL>", "phoneNumber": "13800138000", "role": "user", "userType": "individual", "membershipType": "free", "freeAssessmentCount": 8, "freeAIInterpretationCount": 5, "isActive": true, "createdAt": "2024-01-15T10:30:00.000Z"}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "401": {"description": "未授权，token无效、已过期或已退出"}}, "security": [{"bearer": []}], "summary": "获取用户个人信息 (需要登录)", "tags": ["用户"]}}, "/users/check-usage": {"post": {"operationId": "UsersController_checkUsage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckDto"}}}}, "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckResponseDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "检查使用次数权限 (需要登录)", "tags": ["用户"]}}, "/users/consume-usage": {"post": {"operationId": "UsersController_consumeUsage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageCheckDto"}}}}, "responses": {"200": {"description": "消费成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"success": true, "message": "使用成功", "remainingCount": 9}}}}}}, "400": {"description": "次数不足或需要付费"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "消费使用次数 (需要登录)", "tags": ["用户"]}}, "/users/membership": {"get": {"operationId": "UsersController_getMembershipStatus", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MembershipStatusDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取会员状态 (需要登录)", "tags": ["用户"]}, "patch": {"operationId": "UsersController_updateMembership", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMembershipDto"}}}}, "responses": {"200": {"description": "更新成功"}, "401": {"description": "未授权"}, "403": {"description": "权限不足"}}, "security": [{"bearer": []}], "summary": "更新会员信息 (需要管理员权限)", "tags": ["用户"]}}, "/users": {"get": {"operationId": "UsersController_findAll", "parameters": [], "responses": {"200": {"description": "获取成功"}}, "summary": "获取所有用户", "tags": ["用户"]}}, "/users/{id}": {"get": {"operationId": "UsersController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "400": {"description": "用户不存在"}}, "summary": "根据ID获取用户", "tags": ["用户"]}, "patch": {"operationId": "UsersController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "更新成功"}, "400": {"description": "用户不存在或出生日期已锁定"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "更新用户信息 (需要登录)", "tags": ["用户"]}, "delete": {"operationId": "UsersController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "删除成功"}, "400": {"description": "用户不存在"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "删除用户 (需要管理员权限)", "tags": ["用户"]}}, "/jwt-test/verify-token": {"post": {"operationId": "JwtTestController_verifyToken", "parameters": [{"name": "X-JWT-Token", "required": true, "in": "header", "description": "JWT Bearer token", "schema": {"type": "string"}}], "responses": {"200": {"description": "Token验证成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"valid": true, "payload": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "issuedAt": "2024-01-15T10:00:00.000Z", "expiresAt": "2024-01-15T11:00:00.000Z", "timeToExpire": "45分钟23秒"}}}}}}, "401": {"description": "Token无效或已过期"}}, "summary": "验证JWT token是否有效", "tags": ["JWT测试"]}}, "/jwt-test/test-auth": {"get": {"operationId": "JwtTestController_testAuth", "parameters": [], "responses": {"200": {"description": "认证成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"authenticated": true, "user": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "message": "JWT认证守卫测试通过"}}}}}}, "401": {"description": "未授权，token无效或已过期"}}, "security": [{"bearer": []}], "summary": "测试JWT认证守卫 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/refresh-token": {"post": {"operationId": "JwtTestController_refreshToken", "parameters": [], "responses": {"200": {"description": "Token刷新成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"oldToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "newToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expiresAt": "2024-01-15T12:00:00.000Z", "message": "Token刷新成功"}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "刷新JWT token (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/token-info": {"get": {"operationId": "JwtTestController_getTokenInfo", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "payload": {"sub": "1", "name": "张三", "email": "<EMAIL>", "role": "user", "iat": 1642248000, "exp": 1642251600}, "header": {"alg": "HS256", "typ": "JWT"}, "issuedAt": "2024-01-15T10:00:00.000Z", "expiresAt": "2024-01-15T11:00:00.000Z", "timeToExpire": "45分钟23秒", "isBlacklisted": false}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取当前token详细信息 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/test-permissions": {"get": {"operationId": "JwtTestController_testPermissions", "parameters": [], "responses": {"200": {"description": "权限测试成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"userId": 1, "role": "user", "permissions": {"canAccessProfile": true, "canCreateInvite": false, "canMakePayment": true, "hasTeamPermission": false, "hasIndividualPermission": true}, "userInfo": {"id": 1, "name": "张三", "email": "<EMAIL>", "membershipType": "free", "permissionLevel": "individual"}, "message": "权限测试完成"}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "测试用户权限信息 (需要登录)", "tags": ["JWT测试"]}}, "/jwt-test/generate-test-token": {"post": {"operationId": "JwtTestController_generateTestToken", "parameters": [], "responses": {"200": {"description": "Token生成成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "payload": {"sub": "999", "name": "测试用户", "email": "<EMAIL>", "role": "user"}, "expiresAt": "2024-01-15T11:00:00.000Z", "message": "测试Token生成成功"}}}}}}, "400": {"description": "参数错误"}}, "summary": "生成测试用的JWT token (仅用于开发测试)", "tags": ["JWT测试"]}}, "/cards": {"post": {"operationId": "CardsController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCardDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Cards"]}, "get": {"operationId": "CardsController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}}, "/cards/{id}": {"get": {"operationId": "CardsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}, "patch": {"operationId": "CardsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCardDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Cards"]}, "delete": {"operationId": "CardsController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Cards"]}}, "/coze/conversations": {"post": {"description": "为当前用户创建一个新的AI聊天对话会话。每个对话会话都有唯一的ID，用于后续的消息交互。", "operationId": "CozeController_createConversation", "parameters": [], "requestBody": {"required": true, "description": "创建对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateConversationDto"}, "examples": {"basic": {"summary": "基础对话创建", "description": "创建一个简单的对话会话", "value": {"title": "我的AI编程助手", "region": "zh"}}, "withInitialMessage": {"summary": "包含初始消息的对话", "description": "创建对话并发送第一条消息", "value": {"title": "深度学习讨论", "initialMessage": "你好，我想了解深度学习的基础概念", "region": "zh", "metadata": {"topic": "machine_learning", "difficulty": "beginner"}}}, "withMetadata": {"summary": "包含元数据的对话", "description": "创建带有自定义元数据的对话", "value": {"title": "项目代码审查", "region": "en", "metadata": {"projectName": "my-awesome-app", "language": "typescript", "reviewType": "security"}}}}}}}, "responses": {"201": {"description": "对话创建成功，返回对话详细信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "对话唯一标识符"}, "title": {"type": "string", "description": "对话标题"}, "description": {"type": "string", "description": "对话描述"}, "userId": {"type": "string", "format": "uuid", "description": "用户ID"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}}}}}}, "400": {"description": "请求参数错误，请检查输入数据格式"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "用户权限不足"}, "500": {"description": "服务器内部错误"}}, "summary": "创建新的AI对话会话", "tags": ["AI聊天管理"]}, "get": {"description": "分页获取当前用户的所有AI对话会话列表，支持按创建时间倒序排列。", "operationId": "CozeController_getUserConversations", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认20条，最大100条", "schema": {"example": 20, "type": "number"}}], "responses": {"200": {"description": "对话列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "对话唯一标识符"}, "title": {"type": "string", "description": "对话标题"}, "status": {"type": "string", "enum": ["active", "archived", "deleted"], "description": "对话状态"}, "messageCount": {"type": "number", "description": "消息总数"}, "lastMessageAt": {"type": "string", "format": "date-time", "description": "最后一条消息时间"}, "lastActiveAt": {"type": "string", "format": "date-time", "description": "最后活跃时间"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}, "metadata": {"type": "object", "description": "对话元数据"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总记录数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}}, "example": {"data": [{"id": "550e8400-e29b-41d4-a716-************", "title": "Python Web开发学习", "status": "active", "messageCount": 12, "lastMessageAt": "2024-01-15T14:30:00Z", "lastActiveAt": "2024-01-15T14:30:00Z", "createdAt": "2024-01-15T10:00:00Z", "updatedAt": "2024-01-15T14:30:00Z", "metadata": {"topic": "programming", "difficulty": "intermediate"}}, {"id": "123e4567-e89b-12d3-a456-426614174000", "title": "机器学习基础讨论", "status": "archived", "messageCount": 8, "lastMessageAt": "2024-01-14T16:45:00Z", "lastActiveAt": "2024-01-14T16:45:00Z", "createdAt": "2024-01-14T15:00:00Z", "updatedAt": "2024-01-14T17:00:00Z", "metadata": {"topic": "machine_learning", "completed": true}}], "pagination": {"total": 25, "page": 1, "pageSize": 20, "totalPages": 2, "hasNext": true, "hasPrev": false}}}}}}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "用户权限不足"}, "500": {"description": "服务器内部错误"}}, "summary": "获取用户的AI对话列表", "tags": ["AI聊天管理"]}}, "/coze/conversations/{id}": {"get": {"description": "根据对话ID获取对话的详细信息，包括对话的基本信息和最近的消息概览。", "operationId": "CozeController_getConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "responses": {"200": {"description": "对话详情获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "userId": {"type": "string", "format": "uuid"}, "messageCount": {"type": "number"}, "lastMessageAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "summary": "获取指定对话的详细信息", "tags": ["AI聊天管理"]}, "put": {"description": "更新指定对话的标题、描述等基本信息。只有对话的创建者可以更新对话信息。", "operationId": "CozeController_updateConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "requestBody": {"required": true, "description": "更新对话的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConversationDto"}, "examples": {"updateTitle": {"summary": "更新对话标题", "description": "仅更新对话的标题", "value": {"title": "新的对话标题 - Python Web开发"}}, "updateStatus": {"summary": "更新对话状态", "description": "将对话标记为已归档", "value": {"status": "archived"}}, "updateAll": {"summary": "更新所有可选字段", "description": "同时更新标题、状态和元数据", "value": {"title": "已完成的项目讨论", "status": "archived", "metadata": {"completedAt": "2024-01-15T10:30:00Z", "rating": 5, "tags": ["completed", "helpful", "programming"]}}}, "updateMetadata": {"summary": "仅更新元数据", "description": "添加或更新对话的元数据信息", "value": {"metadata": {"lastTopic": "API设计", "complexity": "intermediate", "estimatedDuration": "45分钟"}}}}}}}, "responses": {"200": {"description": "对话更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "请求参数错误或对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限更新该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "summary": "更新对话信息", "tags": ["AI聊天管理"]}, "delete": {"description": "删除指定的对话及其所有相关消息。此操作不可逆，只有对话的创建者可以删除对话。", "operationId": "CozeController_deleteConversation", "parameters": [{"name": "id", "required": true, "in": "path", "description": "要删除的对话唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}], "responses": {"200": {"description": "对话删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "删除操作是否成功"}}}}}}, "400": {"description": "对话ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限删除该对话"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "summary": "删除对话", "tags": ["AI聊天管理"]}}, "/coze/messages": {"post": {"description": "向AI发送消息并获取回复。使用标准请求-响应模式，等待AI完成回复后一次性返回完整结果。适用于短文本对话。", "operationId": "CozeController_sendMessage", "parameters": [], "requestBody": {"required": true, "description": "发送消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}, "examples": {"simpleText": {"summary": "简单文本消息", "description": "发送一个基础的文本消息给AI", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "你好，请帮我解释一下什么是机器学习？", "type": "text", "streaming": false}}, "programmingQuestion": {"summary": "编程相关问题", "description": "询问具体的编程问题", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "如何在TypeScript中实现单例模式？请提供代码示例。", "type": "text", "streaming": false, "metadata": {"language": "typescript", "category": "design_pattern", "difficulty": "intermediate"}}}, "codeReview": {"summary": "代码审查请求", "description": "请求AI审查代码片段", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请帮我审查这段代码：\n```javascript\nfunction fetchUser(id) {\n  return fetch(`/api/users/${id}`).then(res => res.json());\n}\n```\n有什么可以改进的地方吗？", "type": "text", "streaming": false, "metadata": {"requestType": "code_review", "language": "javascript", "focusAreas": ["error_handling", "best_practices"]}}}, "withObjectString": {"summary": "结构化数据消息", "description": "发送包含结构化数据的消息", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "分析这个API响应数据的结构", "type": "object_string", "streaming": false, "metadata": {"dataType": "api_response", "format": "json"}}}}}}}, "responses": {"201": {"description": "消息发送成功，返回用户消息和AI回复的完整信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"userMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "用户消息ID"}, "conversationId": {"type": "string", "format": "uuid", "description": "对话ID"}, "content": {"type": "string", "description": "用户发送的消息内容"}, "role": {"type": "string", "enum": ["user"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "aiMessage": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "AI消息ID"}, "conversationId": {"type": "string", "format": "uuid", "description": "对话ID"}, "content": {"type": "string", "description": "AI回复的内容"}, "role": {"type": "string", "enum": ["assistant"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间"}}}}, "example": {"userMessage": {"id": "123e4567-e89b-12d3-a456-426614174001", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "你好，请帮我解释一下什么是机器学习？", "role": "user", "type": "text", "status": "completed", "metadata": {}, "createdAt": "2024-01-15T10:30:00Z"}, "aiMessage": {"id": "123e4567-e89b-12d3-a456-426614174002", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能...", "role": "assistant", "type": "answer", "status": "completed", "metadata": {"model": "gpt-3.5-turbo", "temperature": 0.7}, "usage": {"promptTokens": 25, "completionTokens": 150, "totalTokens": 175}, "createdAt": "2024-01-15T10:30:01Z", "completedAt": "2024-01-15T10:30:05Z"}}}}}}, "400": {"description": "请求参数错误或消息内容为空"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话或消息配额不足"}, "404": {"description": "对话不存在"}, "429": {"description": "请求频率过高，请稍后再试"}, "500": {"description": "服务器内部错误或AI服务暂时不可用"}}, "summary": "发送AI消息（标准模式）", "tags": ["AI聊天管理"]}}, "/coze/messages/stream": {"post": {"description": "向AI发送消息并以Server-Sent Events (SSE)方式实时流式接收回复。适用于长文本生成，用户可以实时看到AI的回复过程。", "operationId": "CozeController_streamMessage", "parameters": [], "requestBody": {"required": true, "description": "发送流式消息的请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMessageDto"}, "examples": {"longFormContent": {"summary": "长文本生成请求", "description": "请求AI生成较长的内容，适合流式输出", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请详细介绍一下深度学习的发展历程，包括关键里程碑、重要人物和技术突破", "type": "text", "streaming": true, "metadata": {"expectedLength": "long", "topic": "deep_learning_history"}}}, "codeGeneration": {"summary": "代码生成请求", "description": "请求AI生成较复杂的代码，实时查看生成过程", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请帮我写一个完整的React组件，实现一个带搜索、分页和排序功能的用户列表。要求使用TypeScript和React Hooks。", "type": "text", "streaming": true, "metadata": {"language": "typescript", "framework": "react", "complexity": "high", "features": ["search", "pagination", "sorting"]}}}, "tutorialExplanation": {"summary": "教程式解释", "description": "请求详细的分步骤解释，适合流式展示", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请一步一步地教我如何从零开始构建一个RESTful API，使用Node.js和Express框架", "type": "text", "streaming": true, "metadata": {"instructionType": "step_by_step", "technology": "nodejs_express", "audience": "beginner"}}}, "documentAnalysis": {"summary": "文档分析请求", "description": "分析复杂文档或代码，逐步输出分析结果", "value": {"conversationId": "550e8400-e29b-41d4-a716-************", "content": "请分析这个复杂的SQL查询性能问题，并提供优化建议：\nSELECT u.*, p.*, c.count FROM users u LEFT JOIN profiles p ON u.id = p.user_id LEFT JOIN (SELECT user_id, COUNT(*) as count FROM comments GROUP BY user_id) c ON u.id = c.user_id WHERE u.created_at > '2023-01-01' ORDER BY u.last_login_at DESC", "type": "text", "streaming": true, "metadata": {"analysisType": "sql_performance", "database": "postgresql", "requestType": "optimization"}}}}}}}, "responses": {"200": {"description": "流式消息发送成功，返回SSE数据流", "headers": {"Content-Type": {"description": "text/event-stream"}, "Cache-Control": {"description": "no-cache"}, "Connection": {"description": "keep-alive"}}, "content": {"text/event-stream": {"schema": {"type": "string", "description": "Server-Sent Events格式的数据流，每个事件都是JSON格式", "examples": {"streamEvents": {"summary": "流式事件示例", "description": "展示完整的流式响应过程中的各种事件类型", "value": "data: {\"event\":\"start\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174003\",\"conversationId\":\"550e8400-e29b-41d4-a716-************\"}}\n\ndata: {\"event\":\"chunk\",\"data\":\"深度学习\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"是人工智能\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"的一个重要分支\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"，它通过算法\"}\n\ndata: {\"event\":\"chunk\",\"data\":\"让计算机从数据中学习\"}\n\ndata: {\"event\":\"done\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174003\",\"status\":\"completed\",\"usage\":{\"promptTokens\":32,\"completionTokens\":245,\"totalTokens\":277},\"completedAt\":\"2024-01-15T10:30:15Z\"}}\n\n"}, "errorEvent": {"summary": "错误事件示例", "description": "当流式处理过程中发生错误时的响应格式", "value": "data: {\"event\":\"start\",\"data\":{\"messageId\":\"123e4567-e89b-12d3-a456-426614174004\",\"conversationId\":\"550e8400-e29b-41d4-a716-************\"}}\n\ndata: {\"event\":\"chunk\",\"data\":\"正在处理您的请求\"}\n\ndata: {\"event\":\"error\",\"data\":{\"error\":\"AI服务暂时不可用\",\"code\":\"AI_SERVICE_UNAVAILABLE\",\"messageId\":\"123e4567-e89b-12d3-a456-426614174004\"}}\n\n"}}}}}}, "400": {"description": "请求参数错误或消息内容为空"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话或消息配额不足"}, "404": {"description": "对话不存在"}, "429": {"description": "请求频率过高，请稍后再试"}, "500": {"description": "服务器内部错误或AI服务暂时不可用"}}, "summary": "发送AI消息（流式模式）", "tags": ["AI聊天管理"]}}, "/coze/conversations/{id}/messages": {"get": {"description": "分页获取指定对话中的所有消息记录，按时间倒序排列。包括用户消息和AI回复。", "operationId": "CozeController_getConversationMessages", "parameters": [{"name": "id", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码，从1开始", "schema": {"example": 1, "type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页显示数量，默认50条，最大200条", "schema": {"example": 50, "type": "number"}}], "responses": {"200": {"description": "消息列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "消息唯一标识符"}, "conversationId": {"type": "string", "format": "uuid", "description": "所属对话ID"}, "content": {"type": "string", "description": "消息内容"}, "role": {"type": "string", "enum": ["user", "assistant", "system"], "description": "消息角色"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型"}, "status": {"type": "string", "enum": ["pending", "completed", "failed"], "description": "消息状态"}, "metadata": {"type": "object", "description": "消息元数据"}, "usage": {"type": "object", "properties": {"promptTokens": {"type": "number", "description": "输入Token数量"}, "completionTokens": {"type": "number", "description": "输出Token数量"}, "totalTokens": {"type": "number", "description": "总Token数量"}}, "description": "Token使用量统计（仅AI消息有此字段）"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "completedAt": {"type": "string", "format": "date-time", "description": "完成时间（仅已完成消息有此字段）"}}}}, "pagination": {"type": "object", "properties": {"total": {"type": "number", "description": "总消息数"}, "page": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页大小"}, "totalPages": {"type": "number", "description": "总页数"}, "hasNext": {"type": "boolean", "description": "是否有下一页"}, "hasPrev": {"type": "boolean", "description": "是否有上一页"}}}}, "example": {"data": [{"id": "456e7890-e89b-12d3-a456-426614174005", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进性能。它通过算法分析数据，识别模式，并做出预测或决策。", "role": "assistant", "type": "answer", "status": "completed", "metadata": {"model": "gpt-3.5-turbo", "temperature": 0.7}, "usage": {"promptTokens": 25, "completionTokens": 67, "totalTokens": 92}, "createdAt": "2024-01-15T10:30:01Z", "completedAt": "2024-01-15T10:30:03Z"}, {"id": "789e0123-e89b-12d3-a456-426614174004", "conversationId": "550e8400-e29b-41d4-a716-************", "content": "你好，请帮我解释一下什么是机器学习？", "role": "user", "type": "text", "status": "completed", "metadata": {}, "createdAt": "2024-01-15T10:30:00Z"}], "pagination": {"total": 24, "page": 1, "pageSize": 50, "totalPages": 1, "hasNext": false, "hasPrev": false}}}}}}, "400": {"description": "对话ID格式错误或分页参数错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限访问该对话的消息"}, "404": {"description": "对话不存在"}, "500": {"description": "服务器内部错误"}}, "summary": "获取对话的消息历史", "tags": ["AI聊天管理"]}}, "/coze/conversations/{conversationId}/chats/{chatId}": {"delete": {"description": "取消当前正在进行的AI对话请求。用于停止长时间运行的AI生成任务，特别是流式对话。", "operationId": "CozeController_cancelChat", "parameters": [{"name": "conversationId", "required": true, "in": "path", "description": "对话的唯一标识符", "schema": {"format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "type": "string"}}, {"name": "chatId", "required": true, "in": "path", "description": "聊天会话的标识符，用于标识特定的AI对话请求", "schema": {"example": "chat_1234567890", "type": "string"}}], "responses": {"200": {"description": "聊天取消成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "取消操作是否成功"}}}}}}, "400": {"description": "对话ID或聊天ID格式错误"}, "401": {"description": "用户未认证，请先登录"}, "403": {"description": "无权限取消该聊天"}, "404": {"description": "对话或聊天不存在"}, "410": {"description": "聊天已完成，无法取消"}, "500": {"description": "服务器内部错误"}}, "summary": "取消正在进行的AI对话", "tags": ["AI聊天管理"]}}, "/team-invite/my-invite-code": {"get": {"operationId": "TeamInviteController_getMyInviteCode", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"inviteCode": "ABC12345", "inviteLink": "http://localhost:3000/invite/ABC12345"}}}}}}, "401": {"description": "未授权"}, "403": {"description": "没有邀请权限"}}, "security": [{"bearer": []}], "summary": "获取我的永久邀请码", "tags": ["团队邀请"]}}, "/team-invite/check/{inviteCode}": {"get": {"operationId": "TeamInviteController_checkInviteCode", "parameters": [{"name": "inviteCode", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "检查成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"valid": true, "inviterName": "张三", "inviterId": 1}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "检查邀请码是否有效", "tags": ["团队邀请"]}}, "/team-invite/accept": {"post": {"operationId": "TeamInviteController_acceptInvite", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcceptInviteDto"}}}}, "responses": {"200": {"description": "邀请接受成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"success": true, "message": "邀请接受成功，您已获得团队权限", "teamPermissionGranted": true}}}}}}, "400": {"description": "邀请码无效或已过期"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "接受邀请", "tags": ["团队邀请"]}}, "/team-invite/my-info": {"get": {"operationId": "TeamInviteController_getMyInviteInfo", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "inviteCount": 5, "canInviteOthers": true, "stats": {"totalInvites": 5, "successfulInvites": 5, "pendingInvites": 0, "expiredInvites": 0, "successRate": 100}, "sentInvites": []}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyInviteInfoDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取我的邀请信息", "tags": ["团队邀请"]}}, "/team-invite/my-invited-users": {"get": {"operationId": "TeamInviteController_getMyInvitedUsers", "parameters": [], "responses": {"200": {"description": "获取成功", "schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "totalCount": 3, "invitedUsers": [{"id": 2, "name": "李四", "email": "<EMAIL>", "phoneNumber": "13800138001", "joinedAt": "2024-01-15T10:30:00.000Z", "permissionLevel": "team", "userType": "individual", "membershipType": "free"}, {"id": 3, "name": "王五", "email": "<EMAIL>", "phoneNumber": "13800138002", "joinedAt": "2024-01-16T14:20:00.000Z", "permissionLevel": "team", "userType": "individual", "membershipType": "basic"}], "summary": {"totalUsers": 3, "activeUsers": 3, "teamUsers": 3, "individualUsers": 0}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyInvitedUsersDto"}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取使用我邀请码的用户详细信息", "tags": ["团队邀请"]}}, "/team-invite/my-invited-users-count": {"get": {"operationId": "TeamInviteController_getMyInvitedUsersCount", "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"myInviteCode": "ABC12345", "totalCount": 5, "activeCount": 4, "teamCount": 3}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取使用我邀请码的用户数量统计", "tags": ["团队邀请"]}}, "/team-invite": {"post": {"deprecated": true, "description": "该API已废弃，请使用 GET /my-invite-code 获取永久邀请码", "operationId": "TeamInviteController_createInvite", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInviteDto"}}}}, "responses": {"200": {"description": "返回用户的永久邀请码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteResponseDto"}}}}, "401": {"description": "未授权"}, "403": {"description": "没有邀请权限"}}, "security": [{"bearer": []}], "summary": "创建邀请码（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/detail/{id}": {"get": {"deprecated": true, "description": "该API已废弃，永久邀请码系统不再需要此功能", "operationId": "TeamInviteController_getInviteDetail", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"400": {"description": "该功能已废弃"}}, "security": [{"bearer": []}], "summary": "获取邀请记录详情（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/{id}": {"delete": {"deprecated": true, "description": "该API已废弃，永久邀请码无法取消", "operationId": "TeamInviteController_cancelInvite", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"400": {"description": "永久邀请码无法取消"}}, "security": [{"bearer": []}], "summary": "取消邀请（已废弃）", "tags": ["团队邀请"]}}, "/team-invite/cleanup-expired": {"post": {"deprecated": true, "description": "该API已废弃，永久邀请码不会过期", "operationId": "TeamInviteController_cleanupExpiredInvites", "parameters": [], "responses": {"200": {"description": "永久邀请码不会过期", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"count": 0}}}}}}}, "security": [{"bearer": []}], "summary": "清理过期邀请（已废弃）", "tags": ["团队邀请"]}}, "/payment": {"post": {"operationId": "PaymentController_createPayment", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDto"}}}}, "responses": {"201": {"description": "支付订单创建成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"payment": {"id": 1, "orderNo": "KL1642234567890ABC", "userId": 1, "type": "team_permission", "method": "wechat_pay", "amount": 9900, "status": "pending", "description": "开通团队权限", "createdAt": "2024-01-15T10:30:00.000Z"}, "paymentData": {"orderNo": "KL1642234567890ABC", "amount": 9900, "description": "开通团队权限", "qrCodeUrl": "https://api.example.com/wechat/qr/KL1642234567890ABC", "expireTime": "2024-01-15T11:00:00.000Z"}}}}}}}, "400": {"description": "支付参数错误"}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "创建支付订单", "tags": ["支付"]}}, "/payment/callback/{orderNo}": {"post": {"operationId": "PaymentController_handlePaymentCallback", "parameters": [{"name": "orderNo", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "回调处理成功", "content": {"application/json": {"schema": {"example": {"success": true, "message": "支付成功"}}}}}}, "security": [{"bearer": []}], "summary": "支付回调接口（第三方支付平台调用）", "tags": ["支付"]}}, "/payment/history": {"get": {"operationId": "PaymentController_getPaymentHistory", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 10, "type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"example": {"code": 200, "message": "请求成功", "data": {"payments": [], "total": 0, "page": 1, "limit": 10}}}}}}, "401": {"description": "未授权"}}, "security": [{"bearer": []}], "summary": "获取支付历史记录", "tags": ["支付"]}}, "/payment/detail/{id}": {"get": {"operationId": "PaymentController_getPaymentDetail", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Payment"}}}}, "401": {"description": "未授权"}, "404": {"description": "支付记录不存在"}}, "security": [{"bearer": []}], "summary": "获取支付详情", "tags": ["支付"]}}}, "info": {"title": "KanLi API", "description": "KanLi API Documentation", "version": "1.0", "contact": {}}, "tags": [{"name": "kanli", "description": ""}], "servers": [], "components": {"schemas": {"SendCaptchaDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号", "example": "13800138000", "pattern": "^1[3-9]\\d{9}$"}}, "required": ["phoneNumber"]}, "CreateUserDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号"}, "name": {"type": "string", "description": "用户名", "example": "张三"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "password": {"type": "string", "description": "密码", "example": "Password123!"}, "captcha": {"type": "string", "description": "验证码"}, "fullName": {"type": "string", "description": "真实姓名（可选）"}, "address": {"type": "string", "description": "地址（可选）"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "性别（可选）", "example": "male"}, "birthDate": {"type": "string", "description": "出生日期（可选）", "format": "date", "example": "1990-01-01"}, "userType": {"type": "string", "enum": ["individual", "institution"], "description": "用户类型", "default": "individual", "example": "individual"}, "profilePicture": {"type": "string", "description": "头像链接（可选）"}, "profileBackgroundPicture": {"type": "string", "description": "背景图链接（可选）"}}, "required": ["phoneNumber", "name", "email", "password", "<PERSON><PERSON>a", "userType"]}, "LoginUserDto": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号", "example": "13800138000"}, "password": {"type": "string", "description": "密码", "example": "Password123!"}}, "required": ["phoneNumber", "password"]}, "LogoutResponseDto": {"type": "object", "properties": {"message": {"type": "string", "description": "退出登录消息"}, "timestamp": {"type": "string", "description": "退出时间"}}, "required": ["message", "timestamp"]}, "UserResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "用户唯一标识"}, "name": {"type": "string", "description": "用户名（昵称）"}, "email": {"type": "string", "description": "用户邮箱（唯一）"}, "role": {"type": "string", "enum": ["admin", "user"], "description": "用户角色"}, "userType": {"type": "string", "enum": ["individual", "institution"], "description": "用户类型：个人或机构"}, "fullName": {"type": "string", "description": "真实姓名（可选）"}, "phoneNumber": {"type": "string", "description": "手机号（唯一）"}, "address": {"type": "string", "description": "地址（可选）"}, "gender": {"type": "string", "enum": ["male", "female", "other"], "description": "性别（可选）"}, "birthDate": {"type": "string", "description": "出生日期（可选）", "format": "date"}, "isActive": {"type": "boolean", "description": "账号是否激活"}, "profilePicture": {"type": "string", "description": "头像链接（可选）"}, "profileBackgroundPicture": {"type": "string", "description": "背景图链接（可选）"}, "membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间（可选）", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限"}, "freeAssessmentCount": {"type": "number", "description": "免费评估次数"}, "freeAIInterpretationCount": {"type": "number", "description": "免费 AI 解读次数"}, "totalAssessmentCount": {"type": "number", "description": "总评估次数"}, "totalAIInterpretationCount": {"type": "number", "description": "总 AI 解读次数"}, "lastLoginAt": {"type": "string", "description": "上次登录时间", "format": "date-time"}, "loginCount": {"type": "number", "description": "登录次数"}, "birthDateLocked": {"type": "boolean", "description": "是否锁定出生日期"}, "createdAt": {"type": "string", "description": "创建时间", "format": "date-time"}, "updatedAt": {"type": "string", "description": "更新时间", "format": "date-time"}}, "required": ["id", "name", "email", "role", "userType", "phoneNumber", "isActive", "membershipType", "hasTeamPermission", "freeAssessmentCount", "freeAIInterpretationCount", "totalAssessmentCount", "totalAIInterpretationCount", "loginCount", "birthDateLocked", "createdAt", "updatedAt"]}, "UsageCheckDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["assessment", "aiInterpretation"], "description": "使用类型：评估或AI解读", "example": "assessment"}}, "required": ["type"]}, "UsageCheckResponseDto": {"type": "object", "properties": {"canUse": {"type": "boolean", "description": "是否可以使用（true表示可以免费使用或会员权限，false表示需要付费）"}, "remainingFreeCount": {"type": "number", "description": "剩余免费次数"}, "isMember": {"type": "boolean", "description": "是否为会员"}, "membershipExpireDate": {"format": "date-time", "type": "string", "description": "会员过期时间（如果是会员）"}, "message": {"type": "string", "description": "提示信息"}}, "required": ["canUse", "remainingFreeCount", "isMember", "message"]}, "MembershipStatusDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限"}, "isActive": {"type": "boolean", "description": "会员是否有效"}, "remainingDays": {"type": "number", "description": "剩余天数（如果会员有效）"}}, "required": ["membershipType", "hasTeamPermission", "isActive"]}, "UpdateMembershipDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间", "format": "date-time", "example": "2024-12-31T23:59:59.000Z"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限（可选）"}}, "required": ["membershipType", "membershipExpireDate"]}, "UpdateUserDto": {"type": "object", "properties": {"membershipType": {"type": "string", "enum": ["free", "basic", "premium", "team"], "description": "会员类型（可选）"}, "membershipExpireDate": {"type": "string", "description": "会员过期时间（可选）", "format": "date-time"}, "hasTeamPermission": {"type": "boolean", "description": "是否拥有团队权限（可选）"}, "isActive": {"type": "boolean", "description": "是否激活账号（可选）"}, "birthDateLocked": {"type": "boolean", "description": "是否锁定出生日期（可选）"}}}, "CreateCardDto": {"type": "object", "properties": {}}, "UpdateCardDto": {"type": "object", "properties": {}}, "CreateConversationDto": {"type": "object", "properties": {"title": {"type": "string", "description": "对话标题"}, "initialMessage": {"type": "string", "description": "初始消息"}, "region": {"type": "string", "description": "使用的区域", "default": "zh"}, "metadata": {"type": "object", "description": "对话元数据"}}, "required": ["title", "region"]}, "UpdateConversationDto": {"type": "object", "properties": {"title": {"type": "string", "description": "对话标题"}, "status": {"type": "string", "enum": ["active", "archived", "deleted"], "description": "对话状态"}, "metadata": {"type": "object", "description": "对话元数据"}}}, "CreateMessageDto": {"type": "object", "properties": {"conversationId": {"type": "string", "description": "对话ID"}, "content": {"type": "string", "description": "消息内容"}, "type": {"type": "string", "enum": ["text", "image", "file", "object_string", "answer", "function_call", "tool_output"], "description": "消息类型", "default": "text"}, "streaming": {"type": "boolean", "description": "是否使用流式响应", "default": false}, "metadata": {"type": "object", "description": "消息元数据"}}, "required": ["conversationId", "content", "type", "streaming"]}, "AcceptInviteDto": {"type": "object", "properties": {"inviteCode": {"type": "string", "description": "邀请码", "example": "ABC12345", "minLength": 8, "maxLength": 32}}, "required": ["inviteCode"]}, "InviteStatsDto": {"type": "object", "properties": {"totalInvites": {"type": "number", "description": "总邀请数"}, "successfulInvites": {"type": "number", "description": "成功邀请数"}, "pendingInvites": {"type": "number", "description": "待接受邀请数"}, "expiredInvites": {"type": "number", "description": "已过期邀请数"}, "successRate": {"type": "number", "description": "邀请成功率（百分比）"}}, "required": ["totalInvites", "successfulInvites", "pendingInvites", "expiredInvites", "successRate"]}, "MyInviteInfoDto": {"type": "object", "properties": {"myInviteCode": {"type": "string", "description": "我的邀请码"}, "inviteCount": {"type": "number", "description": "邀请人数"}, "stats": {"description": "邀请统计", "allOf": [{"$ref": "#/components/schemas/InviteStatsDto"}]}, "canInviteOthers": {"type": "boolean", "description": "是否可以邀请他人"}, "sentInvites": {"description": "我发送的邀请列表", "type": "array", "items": {"type": "string"}}}, "required": ["myInviteCode", "inviteCount", "stats", "canInviteOthers", "sentInvites"]}, "MyInvitedUsersDto": {"type": "object", "properties": {"myInviteCode": {"type": "string", "description": "我的邀请码"}, "totalCount": {"type": "number", "description": "总邀请人数"}, "invitedUsers": {"description": "使用我邀请码的用户列表", "type": "array", "items": {"type": "string"}}, "summary": {"type": "object", "description": "统计信息"}}, "required": ["myInviteCode", "totalCount", "invitedUsers", "summary"]}, "CreateInviteDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["direct", "link"], "description": "邀请类型（已废弃：永久邀请码统一为DIRECT类型）", "default": "direct", "example": "direct", "deprecated": true}, "expiresAt": {"type": "string", "description": "邀请过期时间（已废弃：永久邀请码不会过期）", "example": "2024-12-31T23:59:59.000Z", "deprecated": true}, "note": {"type": "string", "description": "邀请备注", "example": "邀请同事加入团队"}, "validHours": {"type": "number", "description": "邀请链接有效期（已废弃：永久邀请码不会过期）", "example": 24, "minimum": 1, "maximum": 168, "deprecated": true}}, "required": ["type"]}, "InviteResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "邀请记录ID"}, "inviteCode": {"type": "string", "description": "邀请码"}, "inviterId": {"type": "number", "description": "邀请人ID"}, "inviterName": {"type": "string", "description": "邀请人姓名"}, "inviteeId": {"type": "number", "description": "被邀请人ID"}, "inviteeName": {"type": "string", "description": "被邀请人姓名"}, "status": {"type": "string", "enum": ["pending", "accepted", "expired", "cancelled"], "description": "邀请状态"}, "type": {"type": "string", "enum": ["direct", "link"], "description": "邀请类型"}, "inviteLink": {"type": "string", "description": "邀请链接"}, "expiresAt": {"format": "date-time", "type": "string", "description": "过期时间"}, "acceptedAt": {"format": "date-time", "type": "string", "description": "接受时间"}, "note": {"type": "string", "description": "备注"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "inviteCode", "inviterId", "<PERSON><PERSON><PERSON><PERSON>", "status", "type", "createdAt", "updatedAt"]}, "CreatePaymentDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["team_permission", "assessment_credits", "ai_interpretation_credits"], "description": "支付类型", "example": "team_permission"}, "method": {"type": "string", "enum": ["wechat_pay", "alipay"], "description": "支付方式", "example": "wechat_pay"}, "amount": {"type": "number", "description": "支付金额（分）", "example": 9900, "minimum": 1}, "quantity": {"type": "number", "description": "购买数量（购买次数时使用）", "example": 10, "minimum": 1}, "description": {"type": "string", "description": "支付描述", "example": "开通团队权限"}, "clientIp": {"type": "string", "description": "客户端IP地址", "example": "127.0.0.1"}}, "required": ["type", "method", "amount"]}, "Payment": {"type": "object", "properties": {"id": {"type": "number", "description": "支付记录ID"}, "orderNo": {"type": "string", "description": "订单号"}, "userId": {"type": "number", "description": "用户ID"}, "type": {"type": "string", "enum": ["team_permission", "assessment_credits", "ai_interpretation_credits"], "description": "支付类型"}, "method": {"type": "string", "enum": ["wechat_pay", "alipay"], "description": "支付方式"}, "amount": {"type": "number", "description": "支付金额（分）"}, "quantity": {"type": "number", "description": "购买数量", "default": 1}, "status": {"type": "string", "enum": ["pending", "processing", "success", "failed", "cancelled", "refunded"], "description": "支付状态", "default": "pending"}, "description": {"type": "string", "description": "支付描述"}, "thirdPartyTransactionId": {"type": "string", "description": "第三方支付平台交易号"}, "paidAt": {"format": "date-time", "type": "string", "description": "支付完成时间"}, "refundedAt": {"format": "date-time", "type": "string", "description": "退款时间"}, "refundAmount": {"type": "number", "description": "退款金额（分）"}, "failureReason": {"type": "string", "description": "失败原因"}, "thirdPartyResponse": {"type": "object", "description": "第三方支付响应数据"}, "metadata": {"type": "object", "description": "支付扩展信息"}, "createdAt": {"format": "date-time", "type": "string", "description": "创建时间"}, "updatedAt": {"format": "date-time", "type": "string", "description": "更新时间"}}, "required": ["id", "orderNo", "userId", "type", "method", "amount", "quantity", "status", "createdAt", "updatedAt"]}}}}