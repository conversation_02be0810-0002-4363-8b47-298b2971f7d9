<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import { useUserStore } from '@/store/user'

interface Props {
  pageType: 'basic' | 'daily' | 'current'
}

interface FormData {
  question: string
  name: string
  gender: string
  archetype: string
  firstCard: string
  secondCard: string
  oppositeArchetype: string
  theme: string
  currentStatus: string
  note: string
}

const props = withDefaults(defineProps<Props>(), {
  pageType: 'basic',
})

const emit = defineEmits<{
  submit: [data: FormData]
}>()

const userStore = useUserStore()
const formRef = ref()

// 表单数据
const formData = reactive<FormData>({
  question: '',
  name: '',
  gender: '',
  archetype: '',
  firstCard: '',
  secondCard: '',
  oppositeArchetype: '',
  theme: '',
  currentStatus: '',
  note: '',
})

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    name: [
      { required: true, message: '请输入姓名' },
    ],
    gender: [
      { required: true, message: '请选择性别' },
    ],
    firstCard: [
      { required: true, message: '请输入第一张卡牌' },
    ],
    note: [
      { required: false, message: '' },
    ],
  }

  if (props.pageType === 'basic') {
    return {
      ...baseRules,
      question: [
        { required: true, message: '请输入问题' },
      ],
      archetype: [
        { required: true, message: '请输入原型' },
      ],
      secondCard: [
        { required: true, message: '请输入第二张卡牌' },
      ],
      oppositeArchetype: [
        { required: true, message: '请输入对方原型' },
      ],
    }
  }
  else if (props.pageType === 'daily') {
    return {
      ...baseRules,
      theme: [
        { required: true, message: '请输入今日主题' },
      ],
      currentStatus: [
        { required: true, message: '请描述当前状态' },
      ],
      secondCard: [
        { required: true, message: '请输入第二张卡牌' },
      ],
    }
  }
  else if (props.pageType === 'current') {
    return {
      ...baseRules,
      theme: [
        { required: true, message: '请输入当下主题' },
      ],
      currentStatus: [
        { required: true, message: '请描述当前状态' },
      ],
    }
  }

  return baseRules
})

// 初始化表单数据
function initFormData() {
  if (userStore.isLoggedIn && userStore.userDetails) {
    formData.name = userStore.userDetails.name || ''
    formData.gender = userStore.userDetails.gender || ''
  }
}

// 性别选择验证
function validateGender() {
  if (!formData.gender) {
    uni.showToast({
      title: '请选择性别',
      icon: 'none',
    })
    return false
  }
  return true
}

// 提交表单
async function handleSubmit() {
  try {
    // 先验证性别选择
    if (!validateGender()) {
      return
    }

    // 使用wot-ui的表单验证
    const { valid } = await formRef.value.validate()
    if (valid) {
      emit('submit', { ...formData })
    }
  }
  catch (error) {
    // 表单验证失败，wot-ui会自动显示错误信息
    console.error('表单验证失败:', error)
  }
}

// 监听pageType变化，重新初始化表单
watch(() => props.pageType, () => {
  initFormData()
}, { immediate: true })

// 暴露重置方法
defineExpose({
  resetForm: () => {
    formRef.value?.reset()
    Object.assign(formData, {
      question: '',
      name: '',
      gender: '',
      archetype: '',
      firstCard: '',
      secondCard: '',
      oppositeArchetype: '',
      theme: '',
      currentStatus: '',
      note: '',
    })
    initFormData()
  },
})
</script>

<template>
  <view class="chat-form-container">
    <wd-form ref="formRef" :model="formData" :rules="formRules" error-type="toast">
      <!-- 表单标题 -->
      <!-- <view class="form-title">
        <text v-if="pageType === 'basic'">
          基础测评
        </text>
        <text v-else-if="pageType === 'daily'">
          今日提醒
        </text>
        <text v-else-if="pageType === 'current'">
          当下状态
        </text>
      </view> -->

      <wd-cell-group border>
        <!-- 基础测评表单 -->
        <template v-if="pageType === 'basic'">
          <wd-input
            v-model="formData.question" label="问题" label-width="100px" prop="question" placeholder="请输入您的问题"
            required clearable
          />
          <wd-input
            v-model="formData.name" label="姓名" label-width="100px" prop="name" placeholder="请输入姓名" required
            :disabled="true"
          />
          <wd-cell title="性别" title-width="100px" prop="gender" required>
            <view class="gender-selector">
              <wd-button
                size="small" :type="formData.gender === '男' ? 'primary' : 'default'"
                @click="formData.gender = '男'"
              >
                👨 男
              </wd-button>
              <wd-button
                size="small" :type="formData.gender === '女' ? 'primary' : 'default'"
                @click="formData.gender = '女'"
              >
                👩 女
              </wd-button>
            </view>
          </wd-cell>
          <wd-input
            v-model="formData.archetype" label="原型" label-width="100px" prop="archetype" placeholder="请输入原型"
            required clearable
          />
          <wd-input
            v-model="formData.firstCard" label="第一张卡牌" label-width="100px" prop="firstCard"
            placeholder="请输入第一张卡牌" required clearable
          />
          <wd-input
            v-model="formData.secondCard" label="第二张卡牌" label-width="100px" prop="secondCard"
            placeholder="请输入第二张卡牌" required clearable
          />
          <wd-input
            v-model="formData.oppositeArchetype" label="对方原型" label-width="100px" prop="oppositeArchetype"
            placeholder="请输入对方原型" required clearable
          />
          <!-- <wd-textarea
            v-model="formData.note" label="备注" label-width="100px" prop="note" placeholder="请输入备注信息（可选）"
            :maxlength="200" show-word-limit clearable
          /> -->
        </template>

        <!-- 今日提醒表单 -->
        <template v-if="pageType === 'daily'">
          <wd-input
            v-model="formData.name" label="姓名" label-width="100px" prop="name" placeholder="请输入姓名" required
            :disabled="true"
          />
          <wd-cell title="性别" title-width="100px" prop="gender" required>
            <view class="gender-selector">
              <wd-button
                size="small" :type="formData.gender === '男' ? 'primary' : 'default'"
                @click="formData.gender = '男'"
              >
                👨 男
              </wd-button>
              <wd-button
                size="small" :type="formData.gender === '女' ? 'primary' : 'default'"
                @click="formData.gender = '女'"
              >
                👩 女
              </wd-button>
            </view>
          </wd-cell>
          <wd-input
            v-model="formData.theme" label="今日主题" label-width="100px" prop="theme" placeholder="请输入今日主题"
            required clearable
          />
          <wd-input
            v-model="formData.currentStatus" label="当前状态" label-width="100px" prop="currentStatus"
            placeholder="请描述您的当前状态" required clearable
          />
          <wd-input
            v-model="formData.firstCard" label="第一张卡牌" label-width="100px" prop="firstCard"
            placeholder="请输入第一张卡牌" required clearable
          />
          <wd-input
            v-model="formData.secondCard" label="第二张卡牌" label-width="100px" prop="secondCard"
            placeholder="请输入第二张卡牌" required clearable
          />
          <!-- <wd-textarea
            v-model="formData.note" label="备注" label-width="100px" prop="note" placeholder="请输入备注信息（可选）"
            :maxlength="200" show-word-limit clearable
          /> -->
        </template>

        <!-- 当下状态表单 -->
        <template v-if="pageType === 'current'">
          <wd-input
            v-model="formData.name" label="姓名" label-width="100px" prop="name" placeholder="请输入姓名" required
            :disabled="true"
          />
          <wd-cell title="性别" title-width="100px" prop="gender" required>
            <view class="gender-selector">
              <wd-button
                size="small" :type="formData.gender === '男' ? 'primary' : 'default'"
                @click="formData.gender = '男'"
              >
                👨 男
              </wd-button>
              <wd-button
                size="small" :type="formData.gender === '女' ? 'primary' : 'default'"
                @click="formData.gender = '女'"
              >
                👩 女
              </wd-button>
            </view>
          </wd-cell>
          <wd-input
            v-model="formData.theme" label="当下主题" label-width="100px" prop="theme" placeholder="请输入当下主题"
            required clearable
          />
          <wd-input
            v-model="formData.currentStatus" label="当前状态" label-width="100px" prop="currentStatus"
            placeholder="请描述您的当前状态" required clearable
          />
          <wd-input
            v-model="formData.firstCard" label="第一张卡牌" label-width="100px" prop="firstCard"
            placeholder="请输入第一张卡牌" required clearable
          />
          <!-- <wd-textarea
            v-model="formData.note" label="备注" label-width="100px" prop="note" placeholder="请输入备注信息（可选）"
            :maxlength="200" show-word-limit clearable
          /> -->
        </template>
      </wd-cell-group>

      <!-- 提交按钮 -->
      <view class="form-footer">
        <wd-button type="primary" size="large" block @click="handleSubmit">
          开始AI解析
        </wd-button>
      </view>
    </wd-form>
  </view>
</template>

<style scoped>
.chat-form-container {
  background-color: transparent;
  padding: 24rpx;
  min-height: 100%;
}

.form-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 32rpx;
  padding: 32rpx 0 24rpx;
  border-bottom: 2rpx solid #6f42c1;
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
}

.gender-selector {
  display: flex;
  gap: 16rpx;
  width: 100%;
}

.gender-selector :deep(.wd-button) {
  flex: 1;
  min-width: 120rpx;
}

.form-footer {
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 0 0 16rpx 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 自定义wot-ui组件样式 */
:deep(.wd-cell-group) {
  border-radius: 0;
  overflow: hidden;
}

:deep(.wd-cell) {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
}

:deep(.wd-input) {
  --wd-input-padding: 0;
  --wd-input-border-color: #e9ecef;
  --wd-input-focus-border-color: #6f42c1;
}

:deep(.wd-textarea) {
  --wd-textarea-padding: 0;
  --wd-textarea-border-color: #e9ecef;
  --wd-textarea-focus-border-color: #6f42c1;
}

:deep(.wd-button--primary) {
  --wd-button-primary-bg: linear-gradient(135deg, #6f42c1 0%, #8b5cf6 100%);
  --wd-button-primary-border-color: #6f42c1;
  box-shadow: 0 8rpx 24rpx rgba(111, 66, 193, 0.3);
}

:deep(.wd-button--primary:active) {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(111, 66, 193, 0.3);
}

/* 响应式调整 */
@media (max-width: 320px) {
  .form-title {
    font-size: 36rpx;
  }

  .form-footer {
    padding: 24rpx;
  }
}
</style>
