# 微信小程序流式传输修复方案（最终版）

## 问题分析

原始错误：`request:fail timeout`

### 根本原因
1. **未使用微信小程序的真正流式传输API**：之前的实现没有使用 `enableChunked` 和 `onChunkReceived`
2. **超时设置不当**：120秒的超时时间对于长时间的AI响应可能不够
3. **数据处理方式错误**：没有正确处理分块传输的数据

## 修复方案（基于官方文档）

### 1. 使用真正的流式传输
- 设置 `enableChunked: true` 开启流式传输模式
- 设置 `responseType: 'arraybuffer'` 接收二进制数据
- 使用 `onChunkReceived` 监听分块数据接收事件

### 2. 增加超时时间
- 将流式请求超时时间从120秒增加到300秒（5分钟）
- 在请求拦截器中为流式请求设置特殊的超时时间

### 3. 实时数据处理
- 使用 `TextDecoder` 将 ArrayBuffer 转换为字符串
- 累积接收到的数据块
- 实时解析完整的事件并立即发送给前端
- 移除打字机效果，直接显示内容（因为真正的流式传输已经提供了逐步效果）

## 修改的文件

### src/api/chat.ts
- 重写 `sendMessageStream` 方法，使用真正的流式传输
- 设置 `enableChunked: true` 和 `responseType: 'arraybuffer'`
- 添加 `onChunkReceived` 监听器
- 新增 `arrayBufferToString` 方法进行数据转换
- 新增 `processStreamChunks` 方法处理分块数据

### src/interceptors/request.ts
- 为流式请求设置特殊超时时间（300秒）

### src/composables/use-real-chat.ts
- 简化 `updateStreamingMessage` 方法，移除打字机效果
- 直接显示流式内容

## 工作原理（真正的流式传输）

1. 用户发送消息
2. 前端发起流式请求，设置 `enableChunked: true`
3. 后端开始流式返回数据
4. 微信小程序通过 `onChunkReceived` 实时接收数据块
5. 前端将 ArrayBuffer 转换为字符串并累积
6. 实时解析完整的事件并立即显示
7. 用户看到真正的实时流式效果

## 关键技术点

### 1. 微信小程序流式传输配置
```javascript
const requestTask = uni.request({
  enableChunked: true,        // 开启流式传输
  responseType: 'arraybuffer', // 接收二进制数据
  timeout: 300000,            // 5分钟超时
})

// 监听分块数据
requestTask.onChunkReceived?.((response) => {
  const chunk = arrayBufferToString(response.data)
  // 处理数据块
})
```

### 2. 数据转换
```javascript
// 使用 TextDecoder 转换 ArrayBuffer
const decoder = new TextDecoder('utf-8')
const text = decoder.decode(buffer)
```

### 3. 实时事件处理
- 累积接收到的数据块
- 按行分割并查找完整的事件
- 立即解析并发送事件给前端组件

## 预期效果

- ✅ 解决超时问题
- ✅ 实现真正的实时流式传输
- ✅ 保持良好的用户体验
- ✅ 完全兼容微信小程序

## 测试建议

1. 在微信开发者工具中测试基本功能
2. **重要**：在真机上测试流式传输效果（开发者工具可能无法完全模拟）
3. 测试长时间的AI响应（超过2分钟）
4. 测试网络不稳定情况下的表现
5. 验证实时流式效果的流畅性
6. 确认错误处理的正确性

## 注意事项

1. **基础库版本**：确保微信基础库版本支持 `enableChunked`（建议 2.25.4+）
2. **真机测试**：流式传输功能需要在真机上测试，开发者工具可能不完全支持
3. **网络环境**：在不同网络环境下测试稳定性
